"""
BarTokenizer 单元测试

测试 BarTokenizer 的各种功能，包括：
1. 不同映射策略的测试
2. 平衡策略的测试
3. 多周期tokenizer的测试
4. 分布平衡性分析
5. 模型保存和加载
"""

import unittest
import numpy as np
import pandas as pd
import tempfile
import os
from unittest.mock import patch
from pyqlab.data.utils import load_single_data

# 添加项目路径
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from pyqlab.data.tokenizers.bar_tokenizer import (
    BarTokenizer,
    MultiPeriodBarTokenizer,
    LinearMapping,
    QuantileMapping,
    AdaptiveMapping,
    FrequencyBalancing
)


class TestMappingStrategies(unittest.TestCase):
    """测试映射策略"""

    def setUp(self):
        """设置测试数据"""
        np.random.seed(42)
        self.test_data = np.random.randn(1000)

    def test_linear_mapping(self):
        """测试线性映射"""
        mapper = LinearMapping(n_bins=50)
        mapper.fit(self.test_data)

        tokens = mapper.transform(self.test_data)
        reconstructed = mapper.inverse_transform(tokens)

        # 检查token范围
        self.assertTrue(np.all(tokens >= 0))
        self.assertTrue(np.all(tokens < 50))

        # 检查重构数据的合理性
        self.assertEqual(len(reconstructed), len(self.test_data))

    def test_quantile_mapping(self):
        """测试分位数映射"""
        mapper = QuantileMapping(n_bins=50)
        mapper.fit(self.test_data)

        tokens = mapper.transform(self.test_data)
        reconstructed = mapper.inverse_transform(tokens)

        # 检查token范围
        self.assertTrue(np.all(tokens >= 0))
        self.assertTrue(np.all(tokens < 50))

        # 检查重构数据的合理性
        self.assertEqual(len(reconstructed), len(self.test_data))

    def test_adaptive_mapping(self):
        """测试自适应映射"""
        mapper = AdaptiveMapping(n_bins=50, balance_factor=0.5)
        mapper.fit(self.test_data)

        tokens = mapper.transform(self.test_data)
        reconstructed = mapper.inverse_transform(tokens)

        # 检查token范围
        self.assertTrue(np.all(tokens >= 0))
        self.assertTrue(np.all(tokens < len(mapper.bin_centers)))

        # 检查重构数据的合理性
        self.assertEqual(len(reconstructed), len(self.test_data))


class TestBarTokenizer(unittest.TestCase):
    """测试BarTokenizer主类"""

    def setUp(self):
        """设置测试数据"""
        np.random.seed(42)
        n_samples = 500

        # 创建模拟的OHLCV数据
        dates = pd.date_range('2024-01-01', periods=n_samples, freq='5min')
        base_price = 100

        self.df = pd.DataFrame({
            'datetime': dates,
            'open': base_price + np.cumsum(np.random.randn(n_samples) * 0.1),
            'high': 0.0,
            'low': 0.0,
            'close': 0.0,
            'volume': np.random.randint(1000, 10000, n_samples)
        })

        # 生成合理的OHLC数据
        for i in range(n_samples):
            open_price = self.df.loc[i, 'open']
            change = np.random.randn() * 0.5
            close_price = open_price + change

            high_price = max(open_price, close_price) + abs(np.random.randn() * 0.2)
            low_price = min(open_price, close_price) - abs(np.random.randn() * 0.2)

            self.df.loc[i, 'close'] = close_price
            self.df.loc[i, 'high'] = high_price
            self.df.loc[i, 'low'] = low_price

    def test_tokenizer_initialization(self):
        """测试tokenizer初始化"""
        tokenizer = BarTokenizer(
            mapping_strategy='linear',
            balancing_strategy='frequency',
            n_bins=50,
            features=['change', 'body']
        )

        self.assertEqual(tokenizer.mapping_strategy_name, 'linear')
        self.assertEqual(tokenizer.balancing_strategy_name, 'frequency')
        self.assertEqual(tokenizer.n_bins, 50)
        self.assertEqual(tokenizer.features, ['change', 'body'])
        self.assertFalse(tokenizer.is_fitted)

    def test_fit_transform(self):
        """测试拟合和转换"""
        tokenizer = BarTokenizer(
            mapping_strategy='adaptive',
            n_bins=30,
            features=['change', 'body', 'upper_shadow', 'lower_shadow']
        )

        # 拟合和转换
        tokens = tokenizer.fit_transform(self.df)

        # 检查结果
        self.assertTrue(tokenizer.is_fitted)
        self.assertEqual(len(tokens), len(self.df))
        self.assertTrue(np.all(tokens >= 0))

        # 检查词汇表大小
        vocab_size = tokenizer.get_vocab_size()
        self.assertGreater(vocab_size, 0)

    def test_different_mapping_strategies(self):
        """测试不同的映射策略"""
        strategies = ['linear', 'quantile', 'adaptive']

        for strategy in strategies:
            with self.subTest(strategy=strategy):
                tokenizer = BarTokenizer(
                    mapping_strategy=strategy,
                    n_bins=20,
                    features=['change', 'body']
                )

                tokens = tokenizer.fit_transform(self.df)

                # 基本检查
                self.assertTrue(tokenizer.is_fitted)
                self.assertEqual(len(tokens), len(self.df))
                self.assertTrue(np.all(tokens >= 0))

    def test_inverse_transform(self):
        """测试逆变换"""
        tokenizer = BarTokenizer(
            mapping_strategy='linear',
            n_bins=20,
            features=['change', 'body']
        )

        tokens = tokenizer.fit_transform(self.df)
        reconstructed = tokenizer.inverse_transform(tokens[:10])

        # 检查重构的特征
        self.assertIsInstance(reconstructed, dict)
        self.assertIn('change', reconstructed)
        self.assertIn('body', reconstructed)

        for feature, values in reconstructed.items():
            self.assertEqual(len(values), 10)

    def test_distribution_analysis(self):
        """测试分布分析"""
        tokenizer = BarTokenizer(
            mapping_strategy='adaptive',
            n_bins=30,
            features=['change', 'body']
        )

        tokens = tokenizer.fit_transform(self.df)

        # 分析平衡性
        balance_metrics = tokenizer.analyze_balance(tokens)

        # 检查指标
        required_metrics = [
            'gini_coefficient', 'entropy', 'max_entropy',
            'normalized_entropy', 'coefficient_of_variation'
        ]

        for metric in required_metrics:
            self.assertIn(metric, balance_metrics)
            self.assertIsInstance(balance_metrics[metric], (int, float))

    def test_model_save_load(self):
        """测试模型保存和加载"""
        tokenizer = BarTokenizer(
            mapping_strategy='quantile',
            n_bins=25,
            features=['change', 'body']
        )

        # 拟合模型
        tokens = tokenizer.fit_transform(self.df)

        # 保存模型
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pkl') as f:
            temp_path = f.name

        try:
            tokenizer.save_model(temp_path)

            # 加载模型
            loaded_tokenizer = BarTokenizer.load_model(temp_path)

            # 检查加载的模型
            self.assertTrue(loaded_tokenizer.is_fitted)
            self.assertEqual(loaded_tokenizer.n_bins, 25)
            self.assertEqual(loaded_tokenizer.features, ['change', 'body'])

            # 测试加载的模型功能
            new_tokens = loaded_tokenizer.transform(self.df)
            np.testing.assert_array_equal(tokens, new_tokens)

        finally:
            if os.path.exists(temp_path):
                os.unlink(temp_path)


class TestMultiPeriodBarTokenizer(unittest.TestCase):
    """测试多周期BarTokenizer"""

    def setUp(self):
        """设置测试数据"""
        np.random.seed(42)
        n_samples = 300

        # 创建基础数据
        dates = pd.date_range('2024-01-01', periods=n_samples, freq='5min')
        base_price = 100

        self.base_df = pd.DataFrame({
            'datetime': dates,
            'open': base_price + np.cumsum(np.random.randn(n_samples) * 0.1),
            'high': 0.0,
            'low': 0.0,
            'close': 0.0,
            'volume': np.random.randint(1000, 10000, n_samples)
        })

        # 生成OHLC数据
        for i in range(n_samples):
            open_price = self.base_df.loc[i, 'open']
            change = np.random.randn() * 0.3
            close_price = open_price + change

            high_price = max(open_price, close_price) + abs(np.random.randn() * 0.15)
            low_price = min(open_price, close_price) - abs(np.random.randn() * 0.15)

            self.base_df.loc[i, 'close'] = close_price
            self.base_df.loc[i, 'high'] = high_price
            self.base_df.loc[i, 'low'] = low_price

        # 创建多周期数据
        self.data_dict = {
            '5min': self.base_df,
            '15min': self.base_df[::3].reset_index(drop=True),
            '1h': self.base_df[::12].reset_index(drop=True)
        }

    def test_multi_period_initialization(self):
        """测试多周期tokenizer初始化"""
        multi_tokenizer = MultiPeriodBarTokenizer(
            periods=['5min', '15min'],
            base_tokenizer_config={
                'mapping_strategy': 'linear',
                'n_bins': 20,
                'features': ['change', 'body']
            }
        )

        self.assertEqual(multi_tokenizer.periods, ['5min', '15min'])
        self.assertEqual(len(multi_tokenizer.tokenizers), 2)
        self.assertFalse(multi_tokenizer.is_fitted)

    def test_multi_period_fit_transform(self):
        """测试多周期拟合和转换"""
        multi_tokenizer = MultiPeriodBarTokenizer(
            periods=['5min', '15min'],
            base_tokenizer_config={
                'mapping_strategy': 'adaptive',
                'n_bins': 15,
                'features': ['change', 'body']
            },
            combination_method='concatenate'
        )

        # 拟合和转换
        multi_tokenizer.fit(self.data_dict)
        tokens = multi_tokenizer.transform(self.data_dict)

        # 检查结果
        self.assertTrue(multi_tokenizer.is_fitted)
        self.assertIsNotNone(multi_tokenizer.vocab_size)
        self.assertGreater(len(tokens), 0)


class TestBalancingStrategies(unittest.TestCase):
    """测试平衡策略"""

    def test_frequency_balancing(self):
        """测试频率平衡策略"""
        # 创建不平衡的token分布
        tokens = np.concatenate([
            np.full(100, 0),  # 大量的0
            np.full(10, 1),   # 少量的1
            np.full(5, 2),    # 更少的2
            np.full(50, 3)    # 中等数量的3
        ])

        balancer = FrequencyBalancing()
        balanced_tokens = balancer.apply(tokens, target_distribution='uniform')

        # 检查结果（这里简化检查，实际应用中会有重采样）
        self.assertEqual(len(balanced_tokens), len(tokens))


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
