"""
CandlestickVQVAETokenizer - 基于VQ-VAE的K线数据Token化器

实现真正的VQ-VAE方法用于K线数据token化，与BarTokenizer进行公平对比。
"""

import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')


class VectorQuantizer(nn.Module):
    """向量量化层"""
    
    def __init__(self, num_embeddings: int, embedding_dim: int, commitment_cost: float = 0.25):
        super().__init__()
        self.num_embeddings = num_embeddings
        self.embedding_dim = embedding_dim
        self.commitment_cost = commitment_cost
        
        # 初始化码本
        self.embedding = nn.Embedding(num_embeddings, embedding_dim)
        self.embedding.weight.data.uniform_(-1/num_embeddings, 1/num_embeddings)
    
    def forward(self, inputs):
        # 输入形状: (batch_size, seq_len, embedding_dim)
        input_shape = inputs.shape
        flat_input = inputs.view(-1, self.embedding_dim)
        
        # 计算距离
        distances = (torch.sum(flat_input**2, dim=1, keepdim=True) 
                    + torch.sum(self.embedding.weight**2, dim=1)
                    - 2 * torch.matmul(flat_input, self.embedding.weight.t()))
        
        # 找到最近的码本向量
        encoding_indices = torch.argmin(distances, dim=1).unsqueeze(1)
        encodings = torch.zeros(encoding_indices.shape[0], self.num_embeddings, device=inputs.device)
        encodings.scatter_(1, encoding_indices, 1)
        
        # 量化
        quantized = torch.matmul(encodings, self.embedding.weight).view(input_shape)
        
        # 计算损失
        e_latent_loss = F.mse_loss(quantized.detach(), inputs)
        q_latent_loss = F.mse_loss(quantized, inputs.detach())
        loss = q_latent_loss + self.commitment_cost * e_latent_loss
        
        # Straight-through estimator
        quantized = inputs + (quantized - inputs).detach()
        
        return quantized, loss, encoding_indices.view(input_shape[:-1])


class CandlestickEncoder(nn.Module):
    """K线数据编码器"""
    
    def __init__(self, input_dim: int, hidden_dim: int, embedding_dim: int):
        super().__init__()
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, embedding_dim)
        )
    
    def forward(self, x):
        return self.encoder(x)


class CandlestickDecoder(nn.Module):
    """K线数据解码器"""
    
    def __init__(self, embedding_dim: int, hidden_dim: int, output_dim: int):
        super().__init__()
        self.decoder = nn.Sequential(
            nn.Linear(embedding_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, output_dim)
        )
    
    def forward(self, x):
        return self.decoder(x)


class CandlestickVQVAE(nn.Module):
    """K线数据VQ-VAE模型"""
    
    def __init__(
        self,
        input_dim: int = 5,
        hidden_dim: int = 64,
        embedding_dim: int = 32,
        num_embeddings: int = 512,
        commitment_cost: float = 0.25
    ):
        super().__init__()
        self.encoder = CandlestickEncoder(input_dim, hidden_dim, embedding_dim)
        self.vq = VectorQuantizer(num_embeddings, embedding_dim, commitment_cost)
        self.decoder = CandlestickDecoder(embedding_dim, hidden_dim, input_dim)
        
    def forward(self, x):
        encoded = self.encoder(x)
        quantized, vq_loss, encoding_indices = self.vq(encoded)
        decoded = self.decoder(quantized)
        
        return decoded, vq_loss, encoding_indices


class CandlestickDataset(Dataset):
    """K线数据集"""
    
    def __init__(self, features: np.ndarray, sequence_length: int = 1):
        self.features = torch.FloatTensor(features)
        self.sequence_length = sequence_length
        
    def __len__(self):
        return len(self.features) - self.sequence_length + 1
    
    def __getitem__(self, idx):
        return self.features[idx:idx + self.sequence_length]


class CandlestickVQVAETokenizer:
    """
    基于VQ-VAE的K线数据Token化器
    """
    
    def __init__(
        self,
        num_embeddings: int = 512,
        embedding_dim: int = 32,
        hidden_dim: int = 64,
        features: List[str] = None,
        atr_period: int = 14,
        device: str = None
    ):
        """
        初始化CandlestickVQVAETokenizer
        
        Args:
            num_embeddings: 码本大小
            embedding_dim: 嵌入维度
            hidden_dim: 隐藏层维度
            features: 特征列表
            atr_period: ATR计算周期
            device: 计算设备
        """
        self.num_embeddings = num_embeddings
        self.embedding_dim = embedding_dim
        self.hidden_dim = hidden_dim
        self.features = features or ['change', 'body', 'upper_shadow', 'lower_shadow', 'volume_ratio']
        self.atr_period = atr_period
        self.device = device or ('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 模型组件
        self.model = None
        self.scaler = None
        self.is_fitted = False
        
        # 统计信息
        self.training_loss = []
        self.vocab_size = num_embeddings
        
    def _calculate_atr(self, df: pd.DataFrame) -> pd.Series:
        """计算ATR"""
        high = df['high'].values
        low = df['low'].values
        close = df['close'].values
        
        # 计算真实波幅
        tr1 = high - low
        tr2 = np.abs(high - np.roll(close, 1))
        tr3 = np.abs(low - np.roll(close, 1))
        
        tr = np.maximum(tr1, np.maximum(tr2, tr3))
        tr[0] = tr1[0]  # 第一个值
        
        # 计算ATR
        atr = pd.Series(tr).rolling(window=self.atr_period).mean()
        atr = atr.bfill().fillna(atr.mean())
        
        return atr
    
    def _extract_features(self, df: pd.DataFrame) -> np.ndarray:
        """提取K线特征"""
        features_list = []
        
        # 计算ATR用于标准化
        atr = self._calculate_atr(df)
        
        # 价格变化特征
        if 'change' in self.features:
            change = (df['close'] - df['close'].shift(1)) / atr
            features_list.append(change.fillna(0))
        
        # K线实体
        if 'body' in self.features:
            body = (df['close'] - df['open']) / atr
            features_list.append(body.fillna(0))
        
        # 上影线
        if 'upper_shadow' in self.features:
            upper_shadow = (df['high'] - df[['open', 'close']].max(axis=1)) / atr
            features_list.append(upper_shadow.fillna(0))
        
        # 下影线
        if 'lower_shadow' in self.features:
            lower_shadow = (df[['open', 'close']].min(axis=1) - df['low']) / atr
            features_list.append(lower_shadow.fillna(0))
        
        # 成交量比率
        if 'volume_ratio' in self.features:
            volume_ma = df['volume'].rolling(window=20).mean()
            volume_ratio = np.log1p(df['volume'] / volume_ma.fillna(volume_ma.mean()))
            features_list.append(volume_ratio.fillna(0))
        
        # 组合特征
        features_matrix = np.column_stack(features_list)
        
        return features_matrix
    
    def _normalize_features(self, features: np.ndarray, fit: bool = False) -> np.ndarray:
        """标准化特征"""
        if fit:
            from sklearn.preprocessing import StandardScaler
            self.scaler = StandardScaler()
            normalized = self.scaler.fit_transform(features)
        else:
            if self.scaler is None:
                raise ValueError("Scaler not fitted")
            normalized = self.scaler.transform(features)
        
        return normalized
    
    def fit(self, df: pd.DataFrame, epochs: int = 100, batch_size: int = 64, lr: float = 1e-3) -> 'CandlestickVQVAETokenizer':
        """
        训练VQ-VAE模型
        
        Args:
            df: K线数据
            epochs: 训练轮数
            batch_size: 批次大小
            lr: 学习率
        """
        print("开始训练VQ-VAE模型...")
        
        # 提取和标准化特征
        features = self._extract_features(df)
        features = self._normalize_features(features, fit=True)
        
        # 创建模型
        input_dim = features.shape[1]
        self.model = CandlestickVQVAE(
            input_dim=input_dim,
            hidden_dim=self.hidden_dim,
            embedding_dim=self.embedding_dim,
            num_embeddings=self.num_embeddings
        ).to(self.device)
        
        # 创建数据集和数据加载器
        dataset = CandlestickDataset(features)
        dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True)
        
        # 优化器
        optimizer = torch.optim.Adam(self.model.parameters(), lr=lr)
        
        # 训练循环
        self.model.train()
        self.training_loss = []
        
        for epoch in range(epochs):
            epoch_loss = 0.0
            num_batches = 0
            
            for batch in dataloader:
                batch = batch.to(self.device)
                
                optimizer.zero_grad()
                
                # 前向传播
                decoded, vq_loss, _ = self.model(batch)
                
                # 重构损失
                recon_loss = F.mse_loss(decoded, batch)
                
                # 总损失
                total_loss = recon_loss + vq_loss
                
                # 反向传播
                total_loss.backward()
                optimizer.step()
                
                epoch_loss += total_loss.item()
                num_batches += 1
            
            avg_loss = epoch_loss / num_batches
            self.training_loss.append(avg_loss)
            
            if (epoch + 1) % 20 == 0:
                print(f"Epoch {epoch+1}/{epochs}, Loss: {avg_loss:.6f}")
        
        self.is_fitted = True
        print("VQ-VAE模型训练完成!")
        
        return self
    
    def transform(self, df: pd.DataFrame) -> np.ndarray:
        """
        将K线数据转换为tokens
        
        Args:
            df: K线数据
            
        Returns:
            tokens: token数组
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted before transform")
        
        # 提取和标准化特征
        features = self._extract_features(df)
        features = self._normalize_features(features, fit=False)
        
        # 转换为tensor
        features_tensor = torch.FloatTensor(features).to(self.device)
        
        # 编码
        self.model.eval()
        with torch.no_grad():
            encoded = self.model.encoder(features_tensor)
            _, _, tokens = self.model.vq(encoded)
        
        return tokens.cpu().numpy().flatten()
    
    def fit_transform(self, df: pd.DataFrame, **kwargs) -> np.ndarray:
        """拟合并转换"""
        return self.fit(df, **kwargs).transform(df)
    
    def inverse_transform(self, tokens: np.ndarray) -> np.ndarray:
        """
        将tokens逆变换为特征值（近似）
        
        Args:
            tokens: token数组
            
        Returns:
            features: 重构的特征
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted before inverse_transform")
        
        # 转换为tensor
        tokens_tensor = torch.LongTensor(tokens).to(self.device)
        
        # 获取量化向量
        quantized = self.model.vq.embedding(tokens_tensor)
        
        # 解码
        self.model.eval()
        with torch.no_grad():
            decoded = self.model.decoder(quantized)
        
        # 逆标准化
        features = self.scaler.inverse_transform(decoded.cpu().numpy())
        
        return features
    
    def get_vocab_size(self) -> int:
        """获取词汇表大小"""
        return self.vocab_size
    
    def get_training_loss(self) -> List[float]:
        """获取训练损失"""
        return self.training_loss
    
    def save_model(self, filepath: str):
        """保存模型"""
        if not self.is_fitted:
            raise ValueError("Model must be fitted before saving")
        
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'scaler': self.scaler,
            'config': {
                'num_embeddings': self.num_embeddings,
                'embedding_dim': self.embedding_dim,
                'hidden_dim': self.hidden_dim,
                'features': self.features,
                'atr_period': self.atr_period
            }
        }, filepath)
    
    @classmethod
    def load_model(cls, filepath: str, device: str = None) -> 'CandlestickVQVAETokenizer':
        """加载模型"""
        checkpoint = torch.load(filepath, map_location=device or 'cpu')
        
        config = checkpoint['config']
        tokenizer = cls(device=device, **config)
        
        # 重建模型
        input_dim = len(config['features'])
        tokenizer.model = CandlestickVQVAE(
            input_dim=input_dim,
            hidden_dim=config['hidden_dim'],
            embedding_dim=config['embedding_dim'],
            num_embeddings=config['num_embeddings']
        ).to(tokenizer.device)
        
        tokenizer.model.load_state_dict(checkpoint['model_state_dict'])
        tokenizer.scaler = checkpoint['scaler']
        tokenizer.is_fitted = True
        
        return tokenizer
