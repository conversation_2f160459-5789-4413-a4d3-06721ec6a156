@echo off
e:
cd e:\lab\RoboQuant\pylab\pyqlab\data\dataset


python analyze_data_quality.py ^
    --data_path "f:/featdata/barenc/db2" ^
    --market "fut" ^
    --period "min1" ^
    --block_name "top" ^
    --save_plots True

@REM python hdl_bar2.py ^
@REM     --data_path "f:/hqdata" ^
@REM     --save_path "f:/featdata/barenc/optimized" ^
@REM     --market "fut" ^
@REM     --period "min1" ^
@REM     --block_name "top" ^
@REM     --year 2025 ^
@REM     --seq_length 30 ^
@REM     --enable_quality_check True ^
@REM     --enable_balance True ^
@REM     --balance_method "adaptive" ^
@REM     --max_token_frequency 0.12 ^
@REM     --max_gap_threshold 5.0 ^
@REM     --min_volume_threshold 100

@REM python hdl_bar2.py ^
@REM     --data_path "f:/hqdata" ^
@REM     --save_path "f:/featdata/barenc/optimized" ^
@REM     --market "fut" ^
@REM     --period "min5" ^
@REM     --block_name "top" ^
@REM     --year 2025 ^
@REM     --seq_length 30 ^
@REM     --enable_quality_check True ^
@REM     --enable_balance True ^
@REM     --balance_method "adaptive" ^
@REM     --max_token_frequency 0.15 ^
@REM     --max_gap_threshold 8.0 ^
@REM     --min_volume_threshold 50

@REM python hdl_bar2.py ^
@REM     --data_path "f:/hqdata" ^
@REM     --save_path "f:/featdata/barenc/optimized" ^
@REM     --market "fut" ^
@REM     --period "day" ^
@REM     --block_name "top" ^
@REM     --year 2025 ^
@REM     --seq_length 30 ^
@REM     --enable_quality_check True ^
@REM     --enable_balance True ^
@REM     --balance_method "undersample" ^
@REM     --max_token_frequency 0.18 ^
@REM     --max_gap_threshold 12.0

python analyze_data_quality.py ^
    --data_path "f:/featdata/barenc/optimized" ^
    --market "fut" ^
    --period "min1" ^
    --block_name "top" ^
    --save_plots True


