from pprint import pprint
import pandas as pd
from datetime import datetime,timed<PERSON>ta
from redis import Redis
import re

class SettlementData():

    def __init__(self, data=None) -> None:
        self.accounts=['中信建投实盘CTP交易-SE', '中信建投实盘CTP交易-XZY', '南华期货实盘CTP交易-SE']
        if data is not None:
            self.data=data.split("\r\n")
            self.data=[s.lstrip().rstrip() for s in self.data]
        self.redis=None
        self.date=datetime.now().strftime('%Y%m%d')
        self.report=pd.DataFrame()

    def SetData(self, data):
        self.data=data.split("\r\n")
        self.data=[s.lstrip().rstrip() for s in self.data]

    def AccountSummary(self) -> dict:
        pos=0
        while True:
            pos+=1
            if pos >= len(self.data):
                return None            
            if self.data[pos][0:4]=='资金状况':
                break
        account={}
        for i in range(12):
            lines=self.data[pos+2+i].split('  ')
            lines=[s.replace('\n', '').replace('%','') for s in lines if s!='']
            if len(lines)==4:
                key=re.sub(r"[^一-龥]", "", lines[0][:-1])
                account[key] = float(lines[1])
                key=re.sub(r"[^一-龥]", "", lines[2][:-1])
                account[key] = float(lines[3])
        return account

    def TransactionRecord(self) -> pd.DataFrame:
        pos=0
        while True:
            pos+=1
            if pos >= len(self.data):
                return None            
            if self.data[pos][0:4]=='成交记录':
                break
        trade=[]
        i=0
        while True:
            lines=self.data[pos+5+i].split('|')
            lines=[s.replace('\n', '').rstrip().lstrip() for s in lines if s!='']
            trade.append(lines)
            i+=1
            if self.data[pos+5+i][:3]=='---':
                break
        if len(trade) == 0:
            return None
        if len(trade[0]) == 15:
            df=pd.DataFrame(trade, columns=['成交日期','交易所','品种','合约','买/卖','投/保','成交价','手数','成交额','开平','手续费','平仓盈亏','权利金收支','成交序号','成交类型'])
            df.drop(columns=['交易编码','资金账号','投/保','权利金收支','成交序号','成交类型'], inplace=True)
        elif len(trade[0]) == 17:
            df=pd.DataFrame(trade, columns=['成交日期','投资单元','交易所','交易编码','品种','合约','买/卖','投/保','成交价','手数','成交额','开平','手续费','平仓盈亏','权利金收支','成交序号','资金账号'])
            df.drop(columns=['投资单元','交易编码','资金账号','投/保','权利金收支','成交序号'], inplace=True)
        else:
            return pd.DataFrame(trade)
        return df

    def PositionClosed(self) -> pd.DataFrame:
        pos=0
        while True:
            pos+=1
            if pos >= len(self.data):
                return None            
            if self.data[pos][0:4]=='平仓明细':
                break
        trade=[]
        i=0
        while True:
            lines=self.data[pos+5+i].split('|')
            lines=[s.replace('\n', '').rstrip().lstrip() for s in lines if s!='']
            trade.append(lines)
            i+=1
            if self.data[pos+5+i][:3]=='---':
                break
        if len(trade) == 0:
            return None
        if len(trade[0]) == 13:
            df=pd.DataFrame(trade, columns=['平仓日期','交易所','品种','合约','开仓日期','买/卖','手数','开仓价','昨结算','成交价','平仓盈亏','权利金收支','成交类型'])
            df.drop(columns=['交易所','昨结算','权利金收支','成交类型'], inplace=True)
        elif len(trade[0]) == 14:
            df=pd.DataFrame(trade, columns=['平仓日期','交易所','品种','合约','开仓日期','买/卖','手数','开仓价','昨结算','成交价','平仓盈亏','权利金收支','成交编号','成交类型'])
            df.drop(columns=['交易所','昨结算','权利金收支','成交编号','成交类型'], inplace=True)
        elif len(trade[0]) == 16:
            df=pd.DataFrame(trade, columns=['平仓日期','投资单元','交易所','交易编码','品种','合约','开仓日期','投/保','买/卖','手数','开仓价','昨结算','成交价','平仓盈亏','权利金收支','资金账号'])
            df.drop(columns=['投资单元','交易所','交易编码','资金账号','投/保','昨结算','权利金收支'], inplace=True)
        else:
            return pd.DataFrame(trade)
        return df

    def PositionsDetail(self) -> pd.DataFrame:
        pos=0
        while True:
            pos+=1
            if pos >= len(self.data):
                return None            
            if self.data[pos][0:4]=='持仓明细':
                break
        trade=[]
        i=0
        while True:
            lines=self.data[pos+5+i].split('|')
            lines=[s.replace('\n', '').rstrip().lstrip() for s in lines if s!='']
            trade.append(lines)
            i+=1
            if self.data[pos+5+i][:3]=='---':
                break
        if len(trade) == 0:
            return None
        if len(trade[0]) == 13:
            df=pd.DataFrame(trade, columns=['品种','合约','买持','买均价','卖持','卖均价','昨结算','今结算','持仓盯市盈亏','保证金占用','投/保','多头期权市值','空头期权市值'])
            df.drop(columns=['多头期权市值','空头期权市值','投/保','昨结算'], inplace=True)
        elif len(trade[0]) == 16:
            df=pd.DataFrame(trade, columns=['投资单元','交易编码','品种','合约','买持','买均价','卖持','卖均价','昨结算','今结算','持仓盯市盈亏','保证金占用','投/保','多头期权市值','空头期权市值','资金账号'])
            df.drop(columns=['投资单元','交易编码','多头期权市值','空头期权市值','资金账号','投/保','昨结算'], inplace=True)
        elif len(trade[0]) == 17:
            df=pd.DataFrame(trade, columns=['投资单元','交易所','交易编码','品种','合约','开仓日期','投/保','买/卖','持仓量','开仓价','昨结算','结算价','浮动盈亏','盯市盈亏','保证金','期权市值','资金账号'])
            df.drop(columns=['投资单元','交易编码','资金账号','投/保','昨结算'], inplace=True)
        else:
            return pd.DataFrame(trade)
        return df

    def Positions(self) -> pd.DataFrame:
        pos=0
        while True:
            pos+=1
            if pos >= len(self.data):
                return None            
            if self.data[pos][0:3]=='持仓汇' or self.data[pos][0:4]=='持仓汇总':
                break
        trade=[]
        i=0
        while True:
            lines=self.data[pos+5+i].split('|')
            lines=[s.replace('\n', '').rstrip().lstrip() for s in lines if s!='']
            trade.append(lines)
            i+=1
            if self.data[pos+5+i][:3]=='---':
                break
        if len(trade) == 0:
            return None
        if len(trade[0]) == 13:
            df=pd.DataFrame(trade, columns=['品种','合约','买持','买均价','卖持','卖均价','昨结算','今结算','持仓盯市盈亏','保证金占用','投/保','多头期权市值','空头期权市值'])
            df.drop(columns=['多头期权市值','空头期权市值','投/保','昨结算'], inplace=True)
        elif len(trade[0]) == 16:
            df=pd.DataFrame(trade, columns=['投资单元','交易编码','品种','合约','买持','买均价','卖持','卖均价','昨结算','今结算','持仓盯市盈亏','保证金占用','投/保','多头期权市值','空头期权市值','资金账号'])
            df.drop(columns=['投资单元','交易编码','多头期权市值','空头期权市值','资金账号','投/保','昨结算'], inplace=True)
        else:
            return pd.DataFrame(trade)
        return df


    def LoadSettlementFromServer(self, date: str):
        if self.redis is None:
            self.redis=Redis(
                host='**************',
                db=3,
                password='wdljshbsjzsszsbbzyjcsz~1974',
                port=51301)
        data=[]
        for acc in self.accounts:
            item=[]
            rpt=self.redis.get(f'settlementreport:{acc}:{date}'.encode('gbk'))
            if rpt is not None and len(rpt)>100:
                item.append(acc)
                item.append(date)
                item.append(rpt.decode('gbk', 'ignore')) 
                data.append(item)
        self.redis.close()
        self.redis=None
        if len(data)>0:
            self.report=pd.DataFrame(data, columns=['account', 'date', 'settlement'])
            return self.report.shape[0]
        return 0

    def _get_last_settlement_date(self):
        dt=datetime.now()
        week=dt.weekday()
        if week==5: # Saturday
            dt=dt + timedelta(days = -1) #减去一天
        elif week==6: # Sunday
            dt=dt + timedelta(days = -2) #减去两天
        elif week==0 and dt.hour<21: # Monday
            dt=dt + timedelta(days = -3) #减去三天
        else:
            if dt.hour<21:
                dt=dt + timedelta(days = -1) #减去一天
        return dt.strftime('%Y%m%d')

    def _LoadSettlement(self, date=None):
        if date is None:
            self.date=self._get_last_settlement_date()
        else:
            self.date=date
        if self.date != date or self.report.empty:
            self.LoadSettlementFromServer(self.date)

    def DisplaySummary(self, content:list, date=None):
        """
        parameter content('account', 'trans', 'closed', 'position')
        """
        self._LoadSettlement(date)
        if not self.report.empty:
            for i in range(self.report.shape[0]):
                self.SetData(self.report.iloc[i, 2])
                print(f"\n====={self.report.iloc[i, 0]}: {self.date}=====")
                if "account" in content:
                    pprint(self.AccountSummary())
                if "trans" in content:
                    print('TransactionRecord-----------------------------------')
                    pprint(self.TransactionRecord())
                if "closed" in content:
                    print('PositionClosed--------------------------------------')
                    pprint(self.PositionClosed())
                if "position" in content:
                    print('Positions-------------------------------------------')
                    df=self.Positions()
                    if df is not None:
                        pprint(df)
                    print('PositionsDetail-------------------------------------')
                    df=self.PositionsDetail()
                    if df is not None:
                        pprint(df)

if __name__ == '__main__':
    from pyqlab.scripts.settlementdata import SettlementData
    sd=SettlementData()
    sd.DisplaySummary(
        content=['account', 'trans', 'closed', 'position'],
        # date='********'
    )