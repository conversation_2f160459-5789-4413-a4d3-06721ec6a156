import os
import numpy as np
import pandas as pd
from typing import List, Dict, Union, Tuple, Optional, Any
from datetime import datetime, timedelta
import glob
from pyqlab.const import MODEL_FUT_CODES

def get_code_id(code: Union[str, int]):
    """获取证券代码ID"""
    try:
        if isinstance(code, str) and len(code) > 2 and code[-2:] != "SH" and code[-2:] != "SZ":
            code_id = MODEL_FUT_CODES.index(code[: -7])
        elif isinstance(code, int):
            code_id = code
        elif code.isdigit():
            code_id = int(code)
    except (ValueError, IndexError) as e:
        print(f"警告: 无法为代码 {code} 获取code_id: {e}，使用默认值0")
        code_id = 0
    return code_id


def get_data_files(data_dir: str, market: str, block_name: str, period: str) -> List[str]:
    """获取数据文件列表"""
   # 查找数据文件
    data_files = []

    # 根据市场和周期筛选数据文件
    if market.lower() == 'all':
        market_pattern = '*'
    else:
        market_pattern = market.lower()

    # 构建文件匹配模式
    if block_name:
        file_pattern = f"{data_dir}/**/{market_pattern}_{block_name}_{period}*.parquet"
    else:
        file_pattern = f"{data_dir}/**/{market_pattern}_{period}*.parquet"

    # 查找匹配的文件
    data_files.extend(glob.glob(file_pattern, recursive=True))

    # 如果没有找到parquet文件，尝试查找csv文件
    if not data_files:
        if block_name:
            file_pattern = f"{data_dir}/**/{market_pattern}_{block_name}_{period}*.csv"
        else:
            file_pattern = f"{data_dir}/**/{market_pattern}_{period}*.csv"

        data_files.extend(glob.glob(file_pattern, recursive=True))

    if not data_files:
        print(f"错误: 在 {data_dir} 中未找到匹配的数据文件")
        return []

    return data_files

def load_single_data(file_path: str, begin_date: str = None, end_date: str = None):
    """
    从parquet或CSV文件加载K线数据

    Args:
        file_path: 文件路径
        begin_date: 开始日期，格式为'YYYY-MM-DD'
        end_date: 结束日期，格式为'YYYY-MM-DD'
        split_ratio: 训练集和验证集的比例，默认为0.0，表示不进行拆分

    Returns:
        包含OHLCV数据的DataFrame列表
    """
    train_data_list = []
    train_code_ids = []

    # 根据文件扩展名选择读取方法
    if file_path.endswith('.parquet'):
        try:
            df = pd.read_parquet(file_path)
        except Exception as e:
            print(f"读取Parquet文件失败: {e}")
            return None
    elif file_path.endswith('.csv'):
        try:
            df = pd.read_csv(file_path)
        except Exception as e:
            print(f"读取CSV文件失败: {e}")
            return None
    else:
        print(f"不支持的文件格式: {file_path}")
        return None

    # 确保datetime列存在
    if 'datetime' not in df.columns:
        # 尝试查找日期时间列
        date_cols = [col for col in df.columns if 'date' in col.lower() or 'time' in col.lower()]
        if date_cols:
            df.rename(columns={date_cols[0]: 'datetime'}, inplace=True)
        else:
            print(f"警告: {file_path}中未找到日期时间列")
            # 创建一个默认的datetime列
            df['datetime'] = pd.date_range(start='2000-01-01', periods=len(df), freq='D')

    # 确保datetime列是datetime类型
    try:
        df['datetime'] = pd.to_datetime(df['datetime'])
    except:
        try:
            # 尝试使用秒级时间戳
            df['datetime'] = pd.to_datetime(df['datetime'], unit='s')
        except:
            print(f"警告: 无法将datetime列转换为日期时间类型，使用默认值")
            df['datetime'] = pd.date_range(start='2000-01-01', periods=len(df), freq='D')

    # 按日期过滤
    if begin_date:
        df = df[df['datetime'] >= datetime.strptime(begin_date, '%Y-%m-%d')]
    if end_date:
        df = df[df['datetime'] <= datetime.strptime(end_date, '%Y-%m-%d')]

    # 检查是否有code列
    if 'code' in df.columns:
        # 按code分组
        for code, group in df.groupby('code'):
            group.set_index('datetime', inplace=True)
            group.sort_index(inplace=True)
            group.reset_index(inplace=True)
            train_data_list.append(group)

            # 尝试获取code_id
            code_id = get_code_id(code)  # 默认值

            train_code_ids.append(code_id)
    else:
        # 没有code列，使用文件名作为code
        code = os.path.basename(file_path).split('.')[0]
        df.set_index('datetime', inplace=True)
        df.sort_index(inplace=True)
        df.reset_index(inplace=True)
        train_data_list.append(df)

        # 尝试获取code_id
        code_id = 0  # 默认值
        try:
            if len(code) > 2 and code[-2:] != "SH" and code[-2:] != "SZ":
                code_id = MODEL_FUT_CODES.index(code[: -7])
            elif isinstance(code, int):
                code_id = code
            elif code.isdigit():
                code_id = int(code)
        except (ValueError, IndexError) as e:
            print(f"警告: 无法为代码 {code} 获取code_id: {e}，使用默认值0")

        train_code_ids.append(code_id)

    # 检查是否有数据
    if len(train_data_list) == 0:
        print(f"警告: {file_path}中没有有效的数据")
        return None

    return train_data_list, train_code_ids
