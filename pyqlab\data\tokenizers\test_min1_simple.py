"""
简化版1分钟数据测试脚本
"""

import os
import sys
import numpy as np
import pandas as pd
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from pyqlab.data.utils import load_single_data
from pyqlab.data.tokenizers.bar_tokenizer import BarTokenizer


def main():
    """简化的1分钟数据测试"""
    print("BarTokenizer 1分钟数据简化测试")
    print("="*50)
    
    # 指定的1分钟数据文件
    file_path = "F:/hqdata/fut_top_min1.parquet"
    
    print(f"正在加载数据: {file_path}")
    
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return
    
    try:
        # 直接读取parquet文件
        df = pd.read_parquet(file_path)
        print(f"原始数据形状: {df.shape}")
        print(f"数据列: {list(df.columns)}")
        
        # 标准化列名
        df.columns = [col.lower() for col in df.columns]
        
        # 检查必要的列
        required_cols = ['open', 'high', 'low', 'close', 'volume']
        if not all(col in df.columns for col in required_cols):
            print(f"缺少必要的列，当前列: {list(df.columns)}")
            return
        
        # 取最近的数据
        df = df.tail(10000).copy()
        
        # 确保数据类型
        for col in required_cols:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # 删除NaN
        df = df.dropna(subset=required_cols)
        
        # 基本数据清理
        df = df[(df['high'] >= df['low']) & 
                (df['high'] >= df['open']) & 
                (df['high'] >= df['close']) &
                (df['low'] <= df['open']) & 
                (df['low'] <= df['close']) &
                (df['volume'] > 0)]
        
        print(f"清理后数据形状: {df.shape}")
        
        if len(df) < 100:
            print("数据量不足")
            return
        
        # 添加datetime列
        if 'datetime' not in df.columns:
            df['datetime'] = pd.date_range(start='2024-01-01', periods=len(df), freq='1min')
        
        print(f"价格范围: {df['close'].min():.2f} - {df['close'].max():.2f}")
        print(f"成交量范围: {df['volume'].min()} - {df['volume'].max()}")
        
        # 测试不同策略
        strategies = ['linear', 'quantile', 'adaptive']
        
        for strategy in strategies:
            print(f"\n--- 测试 {strategy} 策略 ---")
            
            try:
                tokenizer = BarTokenizer(
                    mapping_strategy=strategy,
                    balancing_strategy='frequency',
                    n_bins=100,
                    features=['change', 'body', 'upper_shadow', 'lower_shadow'],
                    atr_period=14
                )
                
                # 拟合和转换
                tokens = tokenizer.fit_transform(df)
                
                # 基本统计
                unique_tokens = len(np.unique(tokens))
                vocab_size = tokenizer.get_vocab_size()
                
                # 计算分布指标
                unique_vals, counts = np.unique(tokens, return_counts=True)
                frequencies = counts / len(tokens)
                
                # 基尼系数
                sorted_freq = np.sort(frequencies)
                n = len(sorted_freq)
                cumsum = np.cumsum(sorted_freq)
                gini = (n + 1 - 2 * np.sum(cumsum)) / n
                
                # 标准化熵
                entropy = -np.sum(frequencies * np.log2(frequencies + 1e-10))
                max_entropy = np.log2(len(unique_vals))
                normalized_entropy = entropy / max_entropy
                
                # 变异系数
                cv = np.std(frequencies) / np.mean(frequencies)
                
                print(f"  生成tokens: {len(tokens)}")
                print(f"  唯一tokens: {unique_tokens}")
                print(f"  词汇表大小: {vocab_size}")
                print(f"  token覆盖率: {unique_tokens/vocab_size:.6f}")
                print(f"  基尼系数: {gini:.6f}")
                print(f"  标准化熵: {normalized_entropy:.6f}")
                print(f"  变异系数: {cv:.6f}")
                print(f"  最高频率: {np.max(frequencies):.6f}")
                print(f"  最低频率: {np.min(frequencies):.6f}")
                
                # 测试逆变换
                sample_tokens = tokens[:5]
                reconstructed = tokenizer.inverse_transform(sample_tokens)
                print(f"  逆变换: 成功重构 {len(reconstructed)} 个特征")
                
            except Exception as e:
                print(f"  策略 {strategy} 失败: {e}")
                import traceback
                traceback.print_exc()
        
        print(f"\n1分钟数据测试完成！")
        
    except Exception as e:
        print(f"测试过程出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
