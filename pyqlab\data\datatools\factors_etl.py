# pip install -i https://pypi.tuna.tsinghua.edu.cn/simple some-package -r requirements.txt
# coding=utf-8
# ## 数据预处理
# 特征向量的构建是机器学习的关键步骤

import sys
import time
from datetime import datetime
import json
import ast
import sqlite3
import pandas as pd
import numpy as np
# from pprint import pprint
from redis import Redis
from typing import Dict

sys.path.append("d:/QuantLab")
from qtunnel import DB,Mode,Cursor,Transaction,create_db,registered_dbs

# ### AICM系统中的因子数据作为机器学习的主要特征向量数据
# - 市场数据
# - 技术指标数据
# - 财务数据
# - 订单上下文数据

# 当前因子数据的产生方式是：通过定义相关特征数据交易策略，并运行策略，
# 当策略产生交易时，同时输出当下的特征因子数据，并写入KV数据库，然后
# 由此脚本导出，清洗，供机器学习使用。

# AICM中的一些常量定义
from pyqlab.const import FACOTR_NUM, ALL_FACTOR_NAMES, TWO_VAL_FACTOR_NAMES, SNAPSHOT_CONTEXT

class AicmFactorsEtl():

    def __init__(self, db_path, data_path, host="", port=0, password="") -> None:
        self.db_path = db_path
        self.data_path = data_path
        self.host = host
        self.port = port
        self.password=password
        self.redis = None

    def _connect_redis(self):
        self.redis=Redis(host=self.host, db=2, password=self.password, port=self.port)

    def set_data(self, key: str, value: str) -> None:
        if not self.redis:
            self._connect_redis()
        return self.redis.set(key, value)

    def hset_data(self, key: str, value: Dict) -> None:
        if not self.redis:
            self._connect_redis()
        for item in value:
            self.redis.hset(key, item, value[item])

    def hgetall_data(self, key):
        if not self.redis:
            self._connect_redis()
        return self.redis.hgetall(key)

    def week_idx_by_ordid(self, ordid) -> int:
        dt=datetime.strptime(ordid[2:14], "%y%m%d%H%M%S")
        return int(dt.timestamp()+259200)//604800 # 开始计数时间为星期四

    def load_factor_from_db(self, week_idx):
        """
        从数据库加载写库因子数据
        """
        db=create_db("leveldb", f"{self.db_path}/store/kv.db" , Mode.read)
        cursor = db.new_cursor()
        # cursor.seek_to_first()
        data = {}
        while cursor.valid():
            # print(cursor.key())
            # print(cursor.key(), cursor.value())
            # fss:lf:
            if cursor.key()[0:4] == b'fss:':
                key = cursor.key().decode()
                ordid = key[7:]
                if week_idx!=-1 and week_idx!=self.week_idx_by_ordid(ordid):
                    cursor.next()
                    continue
                if ordid not in data:
                    data[ordid] = {}
                if key[0:7] == 'fss:lf:':
                    data[ordid]['lf'] = cursor.value().decode()
                if key[0:7] == 'fss:sf:':
                    data[ordid]['sf'] = cursor.value().decode()
                if key[0:7] == 'fss:ct:':
                    data[ordid]['ct'] = cursor.value().decode()
            cursor.next()
        del cursor
        db.close()
        del db

        lf_df = self.get_order_factors(data, factor_type="lf")
        sf_df = self.get_order_factors(data, factor_type="sf")
        ct_df = self.get_order_factors(data, factor_type="ct")

        return lf_df, sf_df, ct_df

    def get_factor_cols(self, factor_type):
        """
        因子列名称
        """
        col_names = ['ord_id']
        if factor_type == "lf":
            for name in ALL_FACTOR_NAMES:
                if name in TWO_VAL_FACTOR_NAMES:
                    col_names.append("{}_1".format(name))
                    col_names.append("{}_2".format(name))
                else:
                    col_names.append("%s_2" % name)

        if factor_type == "sf":
            for name in ALL_FACTOR_NAMES:
                if name in TWO_VAL_FACTOR_NAMES:
                    col_names.append("{}_1".format(name))
                    col_names.append("{}_2".format(name))
                else:
                    col_names.append("%s_2" % name)

        if factor_type == "ct":
            col_names.extend(SNAPSHOT_CONTEXT)

        return col_names

    def get_order_factors(self, ds, factor_type):

        factor_data = []
        for key, value in ds.items():
            if factor_type not in value:
                continue

            item = []
            item.append(np.int64(key))

            if factor_type == "lf":
                data = json.loads(value['lf'])
                for name in ALL_FACTOR_NAMES:
                    if name in data.keys():
                        if name in TWO_VAL_FACTOR_NAMES:
                            item.extend(data[name][1:])
                        else:
                            item.append(data[name][2])
                    else:
                        if name in TWO_VAL_FACTOR_NAMES:
                            item.append(0.0)
                            item.append(0.0)
                        else:
                            item.append(0.0)

            if factor_type == "sf":
                data = json.loads(value['sf'])
                for name in ALL_FACTOR_NAMES:
                    if name in data.keys():
                        if name in TWO_VAL_FACTOR_NAMES:
                            item.extend(data[name][1:])
                        else:
                            item.append(data[name][2])
                    else:
                        if name in TWO_VAL_FACTOR_NAMES:
                            item.append(0.0)
                            item.append(0.0)
                        else:
                            item.append(0.0)

            if factor_type == "ct":
                data = json.loads(value['ct'])
                if 'PNL' not in data.keys():
                    continue
                for key in SNAPSHOT_CONTEXT:
                    if key in data.keys():
                        item.append(data[key])
                    else:
                        item.append(0.0)

            factor_data.append(item)
        ft_name = self.get_factor_cols(factor_type)
        return pd.DataFrame(factor_data, columns=ft_name)


    def load_orders(self, portfolio_id, week_idx):
        """
        从orders数据库导出订单标签
        """
        conn = sqlite3.connect(f'{self.db_path}/data/ot_store.db')
        cur = conn.cursor()

        # cur.execute("""SELECT * FROM T_Filled_Order WHERE account_id = "%s" ORDER BY id DESC"""%('*****************'))
        cur.execute("SELECT * FROM T_Filled_Order ORDER BY id ASC")
        orders = cur.fetchall()

        data = []
        for item in orders:
            record = []
            ord = json.loads(item[1])
            if ord['account_id'] != portfolio_id:
                continue
            if week_idx!=-1 and week_idx!=self.week_idx_by_ordid(ord['order_id']):
                continue
            record.append(np.int64(ord['order_id']))
            record.append(ord['account_id'])
            record.append(ord['order_book_id'])
            record.append(time.strftime("%Y%m%d %H:%M:%S", time.localtime(ord['trading_dt'])))
            msg = ord['message'].split(',')
            if len(msg) <= 1:
                print(msg)
                continue            
            if len(msg) <= 2:
                record.append(msg[0])
                record.append(msg[1])
                record.append(0)
                record.append(0)
            elif len(msg) > 7:
                record.append(msg[0])
                record.append(msg[1])
                record.append(msg[3])
                record.append(msg[7])
            else:
                record.append(msg[0])
                record.append(msg[1])
                record.append(0)
                record.append(0)
            data.append(record)

        cur.close()
        conn.close()

        ord_df = pd.DataFrame(data, columns=['ord_id', 'account_id', 'label', 'datetime', 'oprater', 'direct', 'cost_atr', 'pnl'])
        lb_df = self.get_orders_label(ord_df)
        if len(lb_df) == 0:
            return pd.DataFrame()

        lb_df['CODE'] = lb_df.apply(lambda x: x['instrument'][0:-7], axis=1)

        print(f'Portfolio: {portfolio_id} export order data: {len(lb_df)} \
        Today add count: {(lb_df["datetime"] >= datetime.now().strftime("%Y%m%d 00:00:00")).sum()}')
        return lb_df

    def get_orders_label(self, ord_df):
        data = []
        for _, group in ord_df.groupby(['account_id', 'label']):
            for i in range(len(group)):
                if i + 1 >= len(group):
                    break
                if group.iat[i, 4] != "open":
                    continue
                item = []
                item.append(np.int64(group.iat[i, 0]))
                item.append(group.iat[i, 2])
                i = i + 1
                if group.iat[i, 4] != "open":
                    item.append(group.iat[i, 3])
                    item.append(group.iat[i, 5])
                    if float(group.iat[i, 7]) > 100: # pnl
                        item.append(1)
                    else:
                        item.append(0)
                    data.append(item)
                # else:
                #     print("warning:", group.iat[i, 2])
        return pd.DataFrame(data, columns=['ord_id', 'instrument', 'datetime', 'direct', 'label'])

    def read_factors_from_local(self, portfolio_id, week_idx=None):
        """
        从本地库文件读取因子特征数据
        """
        if not week_idx:
            week_idx=int(datetime.now().timestamp()+259200)//604800
        lb_df = self.load_orders(portfolio_id, week_idx)
        if len(lb_df) == 0:
            print("portfolio: %s not find." % portfolio_id)
            return pd.DataFrame(), pd.DataFrame(), pd.DataFrame(), pd.DataFrame()

        lf_df, sf_df, ct_df = self.load_factor_from_db(week_idx)

        print(len(lb_df), len(lf_df), len(sf_df), len(ct_df))
        ct_df = ct_df[ct_df['ord_id'].isin(lb_df['ord_id'])]
        lb_df = lb_df[lb_df['ord_id'].isin(ct_df['ord_id'])]
        lf_df = lf_df[lf_df['ord_id'].isin(lb_df['ord_id'])]
        sf_df = sf_df[sf_df['ord_id'].isin(lb_df['ord_id'])]
        print(len(lb_df), len(lf_df), len(sf_df), len(ct_df))
        return lb_df, lf_df, sf_df, ct_df

    def read_factors_from_server(self, portfolio_id, week_idx=None):
        """
        从远程库文件读取上载的因子特征数据
        """
        if not week_idx:
            week_idx=int(datetime.now().timestamp()+259200)//604800

        data = self.hgetall_data(f"factors:{portfolio_id}:{week_idx}")
        lb_df = pd.DataFrame.from_dict(ast.literal_eval(data[b"lb"].decode("gb2312")))
        lf_df = pd.DataFrame.from_dict(ast.literal_eval(data[b"lf"].decode("gb2312")))
        sf_df = pd.DataFrame.from_dict(ast.literal_eval(data[b"sf"].decode("gb2312")))
        ct_df = pd.DataFrame.from_dict(ast.literal_eval(data[b"ct"].decode("gb2312", "ignore")))
        return lb_df, lf_df, sf_df, ct_df

    def update_factors_local_file(self, portfolio_id, lb_df, lf_df, sf_df, ct_df):
        """
        将导出的数据更新到最终用于模型学习训练的因子特征数据
        可以以周为时间片段,实现增量更新
        """
        if len(lb_df) == 0:
            print(f"{portfolio_id}: update factors data is empty!")
            return
        for direct in ["long", "short"]:
            if direct=="long":
                tmp_lb_df = lb_df[lb_df['direct'] == 'L']
            else:
                tmp_lb_df = lb_df[lb_df['direct'] == 'S']
            tmp_ct_df = ct_df[ct_df['ord_id'].isin(tmp_lb_df['ord_id'])]
            tmp_lf_df = lf_df[lf_df['ord_id'].isin(tmp_lb_df['ord_id'])]
            tmp_sf_df = sf_df[sf_df['ord_id'].isin(tmp_lb_df['ord_id'])]
            # tmp_lb_df['ord_id'] = tmp_lb_df['ord_id'].astype(np.int64) # 注意:这里的ord_id前面多'00'

            all_lf_df = pd.read_csv(f"{self.data_path}/ft_lf.{direct}.all.{portfolio_id}.csv")
            all_sf_df = pd.read_csv(f"{self.data_path}/ft_sf.{direct}.all.{portfolio_id}.csv")
            all_ct_df = pd.read_csv(f"{self.data_path}/ft_ct.{direct}.all.{portfolio_id}.csv")
            all_lb_df = pd.read_csv(f"{self.data_path}/ft_lb.{direct}.all.{portfolio_id}.csv")
            len0 = len(all_lb_df)

            all_lf_df = pd.concat([all_lf_df, tmp_lf_df], axis=0)
            all_sf_df = pd.concat([all_sf_df, tmp_sf_df], axis=0)
            all_ct_df = pd.concat([all_ct_df, tmp_ct_df], axis=0)
            all_lb_df = pd.concat([all_lb_df, tmp_lb_df], axis=0)
            len1 = len(all_lb_df)

            all_lf_df.drop_duplicates(subset=['ord_id'], keep='first', inplace=True)
            all_sf_df.drop_duplicates(subset=['ord_id'], keep='first', inplace=True)
            all_ct_df.drop_duplicates(subset=['ord_id'], keep='first', inplace=True)
            all_lb_df.drop_duplicates(subset=['ord_id'], keep='first', inplace=True)
            len2 = len(all_lb_df)

            all_lf_df.to_csv(f"{self.data_path}/ft_lf.{direct}.all.{portfolio_id}.csv", index=0)
            all_sf_df.to_csv(f"{self.data_path}/ft_sf.{direct}.all.{portfolio_id}.csv", index=0)
            all_ct_df.to_csv(f"{self.data_path}/ft_ct.{direct}.all.{portfolio_id}.csv", index=0)
            all_lb_df.to_csv(f"{self.data_path}/ft_lb.{direct}.all.{portfolio_id}.csv", index=0)
            print(f">>>{portfolio_id} {direct} len: {len0} -> {len1} -> {len2}  size: {len2-len0}\n")

    def export_factors_data(self, portfolio_id, week_idx=None, save_file=False):
        """
        功能:
            导出一周或全部因子特征数据, 可以保存到文件或服务器REDIS数据库, 
            用于下一步的机器学习数据集的增量更新
        参数:
            week_idx:
                None 导出当前周特征数据
                -1 导出所有特征数据
                n 导出第n周特征数据, 如: n=2720(20220214~20220220)
            save_file:
                True 保持为csv文件
                False 写入redis数据库
        """
        if not week_idx:
            week_idx=int(datetime.now().timestamp()+259200)//604800

        lb_df, lf_df, sf_df, ct_df = self.read_factors_from_local(portfolio_id, week_idx)
        if len(lb_df) == 0:
            print("portfolio: %s not find." % portfolio_id)
            return

        if week_idx==-1:
            week_idx="all"

        # 多空数据分开保存，以便更高效的存取
        if save_file:
            for direct in ["long", "short"]:
                if direct=="long":
                    tmp_lb_df = lb_df[lb_df['direct'] == 'L']
                else:
                    tmp_lb_df = lb_df[lb_df['direct'] == 'S']
                tmp_ct_df = ct_df[ct_df['ord_id'].isin(tmp_lb_df['ord_id'])]
                tmp_lf_df = lf_df[lf_df['ord_id'].isin(tmp_lb_df['ord_id'])]
                tmp_sf_df = sf_df[sf_df['ord_id'].isin(tmp_lb_df['ord_id'])]
                tmp_lf_df.to_csv(f"{self.data_path}/ft_lf.{direct}.{week_idx}.{portfolio_id}.csv", index=0)
                tmp_sf_df.to_csv(f"{self.data_path}/ft_sf.{direct}.{week_idx}.{portfolio_id}.csv", index=0)
                tmp_ct_df.to_csv(f"{self.data_path}/ft_ct.{direct}.{week_idx}.{portfolio_id}.csv", index=0)
                tmp_lb_df.to_csv(f"{self.data_path}/ft_lb.{direct}.{week_idx}.{portfolio_id}.csv", index=0)
        else:
            data={}
            data["lb"] = str(lb_df.to_dict())
            data["lf"] = str(lf_df.to_dict())
            data["sf"] = str(sf_df.to_dict())
            data["ct"] = str(ct_df.to_dict())
            self.hset_data(f"factors:{portfolio_id}:{week_idx}", data)
            print(f"factors:{portfolio_id}:{week_idx}, size: {len(lb_df)}")

    def dump_all_factors_from_local(self, portfolios):
        for pf in portfolios:
            self.export_factors_data(pf, week_idx=-1, save_file=True)

    def dump_all_from_local_to_achive(self, portfolios, data_path="./data", name_suff=""):
        all_lb_df = None
        all_lf_df = None
        all_sf_df = None
        all_ct_df = None
        for portfolio_id in portfolios:
            lb_df, lf_df, sf_df, ct_df = self.read_factors_from_local(portfolio_id, week_idx=-1)
            lb_df.insert(0, "portfolio_id", portfolio_id)
            lf_df.insert(0, "portfolio_id", portfolio_id)
            sf_df.insert(0, "portfolio_id", portfolio_id)
            ct_df.insert(0, "portfolio_id", portfolio_id)
            if all_lb_df is None:
                all_lb_df = lb_df
                all_lf_df = lf_df
                all_sf_df = sf_df
                all_ct_df = ct_df
            else:
                all_lb_df = pd.concat([all_lb_df, lb_df]).reset_index(drop=True)
                all_lf_df = pd.concat([all_lf_df, lf_df]).reset_index(drop=True)
                all_sf_df = pd.concat([all_sf_df, sf_df]).reset_index(drop=True)
                all_ct_df = pd.concat([all_ct_df, ct_df]).reset_index(drop=True)
        # save to parquet files
        all_lb_df.to_parquet(f"{data_path}/lb_df{name_suff}.parquet", engine="fastparquet")
        all_lf_df.to_parquet(f"{data_path}/lf_df{name_suff}.parquet", engine="fastparquet")
        all_sf_df.to_parquet(f"{data_path}/sf_df{name_suff}.parquet", engine="fastparquet")
        all_ct_df.to_parquet(f"{data_path}/ct_df{name_suff}.parquet", engine="fastparquet")

    def upload_a_week_factors(self, portfolios, week_idx=None):
        for pf in portfolios:
            self.export_factors_data(pf, week_idx)      

    def update_a_week_factors_from_server(self, portfolios, week_idx=None):
        for pf in portfolios:
            lb, lf, sf, ct = self.read_factors_from_server(pf, week_idx)
            print(f">>>dwonload from server {pf}, size: {len(lb)}\n")
            print(lb[lb["datetime"] >= datetime.now().strftime("%Y%m%d 00:00:00")])
            print("============================================================\n")
            self.update_factors_local_file(pf, lb, lf, sf, ct)

    def update_a_week_factors_from_local(self, portfolios, week_idx=None):
        for pf in portfolios:
            lb, lf, sf, ct = self.read_factors_from_local(pf, week_idx)
            print(lb[lb["datetime"] >= datetime.now().strftime("%Y%m%d 00:00:00")])
            print("============================================================\n")
            self.update_factors_local_file(pf, lb, lf, sf, ct)

if __name__ == '__main__':
    # 配置
    config_server = {
        "db_path": "c:/TRobot",
        "data_path": 'e:/lab/RoboQuant/pylab/data',
        "host": "**************",
        "port": 51301,
        "password": "wdljshbsjzsszsbbzyjcsz~1974"
    }

    config_local = {
        "db_path": "d:/RoboQuant",
        "data_path": 'e:/lab/RoboQuant/pylab/data',
        "host": "**************",
        "port": 51301,
        "password": "wdljshbsjzsszsbbzyjcsz~1974"
    }

    pfs_name_ids = {
        "zxjt_pjj": {
            "FUT-ZXJT-P21": "00210102215917000",
            "FUT-ZXJT-JMO": "00171009141918000",
            "FUT-ZXJT-JHL": "00210303161416000",
        },
        "zxjt_xzy": {
            "FUT-ZXJT-X21": "00210102224248000",
            "FUT-ZXJT-XMO": "00171009141918000",
            "FUT-ZXJT-XHL": "00210419180454000",
        },
        "nhqh_xzy": {
            "FUT-NHQH-X21": "00210102223323000",
            "FUT-NHQH-W1B": "00170908115033000",
            "FUT-NHQH-NN1": "00220123224825000",
        },
        "gtja_xzy": {
            "FUT-GTJA-X21": "00210102225821000",
            "FUT-GTJA-W1B": "00171009141918000",
        }
    }

    pfs_main = ['00211229152555000', '00170623114649000', '01220901173143000']
    pfs_zxjt_pjj = ['00210102215917000', '00171009141918000', '00210303161416000']
    pfs_zxjt_xzy = ['00210102224248000', '00171009141918000', '00210419180454000']
    pfs_zxjt_nhqh = ['00210102223323000', '00170908115033000', '00220123224825000']
    pfs_zxjt_gtja = ['00210102225821000', '00171009141918000']
    pfs_local = ['00171106132928000', '06220831232331000'] # '*****************', 
    # pfs_local = ['*****************', '00171106132928000', '00170607084458001', '00171122123535000']

    # 导出所有本地数据
    # etl = AicmFactorsEtl(**config_server)
    # etl.dump_all_factors_from_local(pfs_zxjt_pjj)

    etl = AicmFactorsEtl(**config_local)
    etl.dump_all_factors_from_local(pfs_local)

    # 上载数据
    # etl = AicmFactorsEtl(**config_server)
    # etl.upload_a_week_factors(pfs_main)
    # week_idx=2720
    week_idx=int(datetime.now().timestamp()+259200)//604800
    print(f"============== UPDATE FACTOR DATASET: {week_idx}================")
    # 更新服务器数据到本地
    # etl = AicmFactorsEtl(**config_server)
    # etl.update_a_week_factors_from_server(pfs_main, week_idx=week_idx)
    # 更新本地库数据到本地
    # etl = AicmFactorsEtl(**config_local)
    # etl.update_a_week_factors_from_local(pfs_local, week_idx=week_idx)

