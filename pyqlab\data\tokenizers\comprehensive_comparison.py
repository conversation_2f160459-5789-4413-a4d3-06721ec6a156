"""
BarTokenizer vs CandlestickVQVAETokenizer 全面性能对比

使用真实数据在相同条件下对比两种方法的多维度性能。
"""

import os
import sys
import time
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from pyqlab.data.tokenizers.bar_tokenizer import BarTokenizer
from pyqlab.data.tokenizers.candlestick_vqvae_tokenizer import CandlestickVQVAETokenizer


def load_real_data(file_path: str, sample_size: int = 3000) -> pd.DataFrame:
    """加载真实K线数据"""
    print(f"正在加载真实数据: {file_path}")
    
    if not os.path.exists(file_path):
        print(f"文件不存在，使用模拟数据")
        return create_realistic_data(sample_size)
    
    try:
        df = pd.read_parquet(file_path)
        print(f"原始数据: {df.shape[0]} 行")
        
        # 取最近的数据
        df = df.tail(sample_size).copy()
        
        # 标准化列名
        df.columns = [col.lower() for col in df.columns]
        
        # 基本清理
        df = df.dropna(subset=['open', 'high', 'low', 'close', 'volume'])
        df = df[(df['high'] >= df['low']) & 
                (df['high'] >= df['open']) & 
                (df['high'] >= df['close']) &
                (df['low'] <= df['open']) & 
                (df['low'] <= df['close']) &
                (df['volume'] > 0)]
        
        # 确保有datetime列
        if 'datetime' not in df.columns:
            df['datetime'] = pd.date_range(start='2024-01-01', periods=len(df), freq='5min')
        
        df = df.sort_values('datetime').reset_index(drop=True)
        
        print(f"清理后数据: {len(df)} 行")
        print(f"价格范围: {df['close'].min():.2f} - {df['close'].max():.2f}")
        
        return df
        
    except Exception as e:
        print(f"加载真实数据失败: {e}")
        print("使用模拟数据")
        return create_realistic_data(sample_size)


def create_realistic_data(n_samples: int = 3000) -> pd.DataFrame:
    """创建真实感的模拟数据"""
    np.random.seed(42)
    
    dates = pd.date_range('2024-01-01', periods=n_samples, freq='5min')
    
    # 模拟真实的价格走势
    trend = np.linspace(0, 20, n_samples)
    cycle = 5 * np.sin(2 * np.pi * np.arange(n_samples) / 288)
    noise = np.cumsum(np.random.randn(n_samples) * 0.5)
    base_price = 100 + trend + cycle + noise
    
    df = pd.DataFrame({
        'datetime': dates,
        'open': base_price,
        'high': 0.0,
        'low': 0.0,
        'close': 0.0,
        'volume': np.random.lognormal(8, 0.5, n_samples).astype(int)
    })
    
    # 生成OHLC数据
    for i in range(n_samples):
        open_price = df.loc[i, 'open']
        change = np.random.randn() * 0.8
        close_price = open_price + change
        
        high_extra = abs(np.random.randn() * 0.4)
        low_extra = abs(np.random.randn() * 0.4)
        
        high_price = max(open_price, close_price) + high_extra
        low_price = min(open_price, close_price) - low_extra
        
        df.loc[i, 'close'] = close_price
        df.loc[i, 'high'] = high_price
        df.loc[i, 'low'] = low_price
    
    return df


def analyze_token_distribution(tokens: np.ndarray, method_name: str) -> Dict:
    """分析token分布"""
    unique_tokens, counts = np.unique(tokens, return_counts=True)
    frequencies = counts / len(tokens)
    
    # 基尼系数
    sorted_freq = np.sort(frequencies)
    n = len(sorted_freq)
    cumsum = np.cumsum(sorted_freq)
    gini = (n + 1 - 2 * np.sum(cumsum)) / n
    
    # 标准化熵
    entropy = -np.sum(frequencies * np.log2(frequencies + 1e-10))
    max_entropy = np.log2(len(unique_tokens))
    normalized_entropy = entropy / max_entropy
    
    # 变异系数
    cv = np.std(frequencies) / np.mean(frequencies)
    
    # 其他指标
    metrics = {
        'method': method_name,
        'total_tokens': len(tokens),
        'unique_tokens': len(unique_tokens),
        'vocab_coverage': len(unique_tokens) / len(tokens) if len(tokens) > len(unique_tokens) else 1.0,
        'gini_coefficient': gini,
        'normalized_entropy': normalized_entropy,
        'coefficient_of_variation': cv,
        'max_frequency': np.max(frequencies),
        'min_frequency': np.min(frequencies),
        'frequency_range': np.max(frequencies) - np.min(frequencies),
        'top_10_percent_share': np.sum(np.sort(frequencies)[-len(frequencies)//10:])
    }
    
    return metrics


def test_reconstruction_quality(tokenizer, df: pd.DataFrame, tokens: np.ndarray, method_name: str) -> Dict:
    """测试重构质量"""
    print(f"  测试 {method_name} 重构质量...")
    
    try:
        # 取样本进行重构测试
        sample_size = min(100, len(tokens))
        sample_tokens = tokens[:sample_size]
        
        if hasattr(tokenizer, 'inverse_transform'):
            reconstructed = tokenizer.inverse_transform(sample_tokens)
            
            if isinstance(reconstructed, dict):
                # BarTokenizer返回字典
                recon_features = np.column_stack(list(reconstructed.values()))
            else:
                # VQ-VAE返回数组
                recon_features = reconstructed
            
            # 提取原始特征进行对比
            if method_name == 'BarTokenizer':
                original_features = tokenizer._extract_features(df.head(sample_size))
                original_dict = {}
                for i, feature in enumerate(tokenizer.features):
                    if feature in tokenizer.feature_mappers:
                        original_dict[feature] = original_features.iloc[:, i] if hasattr(original_features, 'iloc') else original_features[:, i]
                original_array = np.column_stack(list(original_dict.values()))
            else:
                # VQ-VAE
                original_array = tokenizer._extract_features(df.head(sample_size))
                original_array = tokenizer._normalize_features(original_array, fit=False)
            
            # 计算重构误差
            if recon_features.shape == original_array.shape:
                mse = np.mean((recon_features - original_array) ** 2)
                mae = np.mean(np.abs(recon_features - original_array))
                correlation = np.corrcoef(recon_features.flatten(), original_array.flatten())[0, 1]
            else:
                mse = mae = correlation = np.nan
            
            return {
                'reconstruction_mse': mse,
                'reconstruction_mae': mae,
                'reconstruction_correlation': correlation,
                'reconstruction_success': True
            }
        else:
            return {
                'reconstruction_mse': np.nan,
                'reconstruction_mae': np.nan,
                'reconstruction_correlation': np.nan,
                'reconstruction_success': False
            }
    
    except Exception as e:
        print(f"    重构测试失败: {e}")
        return {
            'reconstruction_mse': np.nan,
            'reconstruction_mae': np.nan,
            'reconstruction_correlation': np.nan,
            'reconstruction_success': False
        }


def comprehensive_comparison(df: pd.DataFrame) -> Dict:
    """全面性能对比"""
    print("=== BarTokenizer vs CandlestickVQVAETokenizer 全面对比 ===\n")
    
    results = {}
    
    # 共同配置
    common_features = ['change', 'body', 'upper_shadow', 'lower_shadow', 'volume_ratio']
    
    # 1. 测试BarTokenizer
    print("1. 测试BarTokenizer")
    print("  配置: Quantile映射, Independent组合")
    
    start_time = time.time()
    
    bar_tokenizer = BarTokenizer(
        mapping_strategy='quantile',
        combination_method='independent',
        n_bins=100,
        features=common_features,
        balancing_strategy='frequency'
    )
    
    bar_tokens = bar_tokenizer.fit_transform(df)
    bar_train_time = time.time() - start_time
    
    # 推理速度测试
    start_time = time.time()
    for _ in range(10):
        _ = bar_tokenizer.transform(df[:100])
    bar_inference_time = (time.time() - start_time) / 10
    
    # 分析分布
    bar_metrics = analyze_token_distribution(bar_tokens, "BarTokenizer")
    
    # 重构质量测试
    bar_recon = test_reconstruction_quality(bar_tokenizer, df, bar_tokens, "BarTokenizer")
    
    results['BarTokenizer'] = {
        'train_time': bar_train_time,
        'inference_time': bar_inference_time,
        'vocab_size': bar_tokenizer.get_vocab_size(),
        'memory_estimate_mb': bar_tokenizer.get_vocab_size() * 512 * 4 / (1024 * 1024),
        **bar_metrics,
        **bar_recon
    }
    
    print(f"  训练时间: {bar_train_time:.3f}秒")
    print(f"  词汇表大小: {bar_tokenizer.get_vocab_size()}")
    print(f"  基尼系数: {bar_metrics['gini_coefficient']:.6f}")
    
    # 2. 测试CandlestickVQVAETokenizer
    print("\n2. 测试CandlestickVQVAETokenizer")
    print("  配置: 512码本, 32维嵌入, 轻量训练")
    
    start_time = time.time()
    
    vqvae_tokenizer = CandlestickVQVAETokenizer(
        num_embeddings=512,
        embedding_dim=32,
        hidden_dim=64,
        features=common_features,
        device='cpu'  # 使用CPU确保公平对比
    )
    
    # 轻量训练（减少epochs以节省时间）
    vqvae_tokens = vqvae_tokenizer.fit_transform(
        df, 
        epochs=50,  # 减少训练轮数
        batch_size=32,
        lr=1e-3
    )
    vqvae_train_time = time.time() - start_time
    
    # 推理速度测试
    start_time = time.time()
    for _ in range(10):
        _ = vqvae_tokenizer.transform(df[:100])
    vqvae_inference_time = (time.time() - start_time) / 10
    
    # 分析分布
    vqvae_metrics = analyze_token_distribution(vqvae_tokens, "CandlestickVQVAE")
    
    # 重构质量测试
    vqvae_recon = test_reconstruction_quality(vqvae_tokenizer, df, vqvae_tokens, "CandlestickVQVAE")
    
    results['CandlestickVQVAE'] = {
        'train_time': vqvae_train_time,
        'inference_time': vqvae_inference_time,
        'vocab_size': vqvae_tokenizer.get_vocab_size(),
        'memory_estimate_mb': vqvae_tokenizer.get_vocab_size() * 512 * 4 / (1024 * 1024),
        'training_loss': vqvae_tokenizer.get_training_loss(),
        **vqvae_metrics,
        **vqvae_recon
    }
    
    print(f"  训练时间: {vqvae_train_time:.3f}秒")
    print(f"  词汇表大小: {vqvae_tokenizer.get_vocab_size()}")
    print(f"  基尼系数: {vqvae_metrics['gini_coefficient']:.6f}")
    print(f"  最终训练损失: {vqvae_tokenizer.get_training_loss()[-1]:.6f}")
    
    return results


def print_detailed_comparison(results: Dict):
    """打印详细对比结果"""
    print("\n" + "="*100)
    print("详细性能对比结果")
    print("="*100)
    
    bar_result = results['BarTokenizer']
    vqvae_result = results['CandlestickVQVAE']
    
    # 基本性能指标
    print(f"\n{'指标':<25} {'BarTokenizer':<20} {'CandlestickVQVAE':<20} {'优势方':<15}")
    print("-" * 85)
    
    # 训练时间
    train_ratio = vqvae_result['train_time'] / bar_result['train_time']
    winner = "BarTokenizer" if train_ratio > 1 else "CandlestickVQVAE"
    print(f"{'训练时间(秒)':<25} {bar_result['train_time']:<20.3f} {vqvae_result['train_time']:<20.3f} {winner:<15}")
    
    # 推理时间
    inference_ratio = vqvae_result['inference_time'] / bar_result['inference_time']
    winner = "BarTokenizer" if inference_ratio > 1 else "CandlestickVQVAE"
    print(f"{'推理时间(秒)':<25} {bar_result['inference_time']:<20.6f} {vqvae_result['inference_time']:<20.6f} {winner:<15}")
    
    # 内存需求
    memory_ratio = vqvae_result['memory_estimate_mb'] / bar_result['memory_estimate_mb']
    winner = "BarTokenizer" if memory_ratio > 1 else "CandlestickVQVAE"
    print(f"{'内存需求(MB)':<25} {bar_result['memory_estimate_mb']:<20.2f} {vqvae_result['memory_estimate_mb']:<20.2f} {winner:<15}")
    
    # 分布平衡性指标
    print(f"\n{'分布平衡性指标':<25} {'BarTokenizer':<20} {'CandlestickVQVAE':<20} {'优势方':<15}")
    print("-" * 85)
    
    # 基尼系数（越小越好）
    gini_winner = "BarTokenizer" if bar_result['gini_coefficient'] < vqvae_result['gini_coefficient'] else "CandlestickVQVAE"
    print(f"{'基尼系数':<25} {bar_result['gini_coefficient']:<20.6f} {vqvae_result['gini_coefficient']:<20.6f} {gini_winner:<15}")
    
    # 标准化熵（越大越好）
    entropy_winner = "BarTokenizer" if bar_result['normalized_entropy'] > vqvae_result['normalized_entropy'] else "CandlestickVQVAE"
    print(f"{'标准化熵':<25} {bar_result['normalized_entropy']:<20.6f} {vqvae_result['normalized_entropy']:<20.6f} {entropy_winner:<15}")
    
    # 变异系数（越小越好）
    cv_winner = "BarTokenizer" if bar_result['coefficient_of_variation'] < vqvae_result['coefficient_of_variation'] else "CandlestickVQVAE"
    print(f"{'变异系数':<25} {bar_result['coefficient_of_variation']:<20.6f} {vqvae_result['coefficient_of_variation']:<20.6f} {cv_winner:<15}")
    
    # 重构质量指标
    print(f"\n{'重构质量指标':<25} {'BarTokenizer':<20} {'CandlestickVQVAE':<20} {'优势方':<15}")
    print("-" * 85)
    
    if bar_result['reconstruction_success'] and vqvae_result['reconstruction_success']:
        # MSE（越小越好）
        mse_winner = "BarTokenizer" if bar_result['reconstruction_mse'] < vqvae_result['reconstruction_mse'] else "CandlestickVQVAE"
        print(f"{'重构MSE':<25} {bar_result['reconstruction_mse']:<20.6f} {vqvae_result['reconstruction_mse']:<20.6f} {mse_winner:<15}")
        
        # 相关性（越大越好）
        corr_winner = "BarTokenizer" if bar_result['reconstruction_correlation'] > vqvae_result['reconstruction_correlation'] else "CandlestickVQVAE"
        print(f"{'重构相关性':<25} {bar_result['reconstruction_correlation']:<20.6f} {vqvae_result['reconstruction_correlation']:<20.6f} {corr_winner:<15}")
    
    # 综合评分
    print(f"\n综合评分:")
    bar_score = 0
    vqvae_score = 0
    
    # 计分规则
    if bar_result['train_time'] < vqvae_result['train_time']: bar_score += 1
    else: vqvae_score += 1
    
    if bar_result['inference_time'] < vqvae_result['inference_time']: bar_score += 1
    else: vqvae_score += 1
    
    if bar_result['memory_estimate_mb'] < vqvae_result['memory_estimate_mb']: bar_score += 1
    else: vqvae_score += 1
    
    if bar_result['gini_coefficient'] < vqvae_result['gini_coefficient']: bar_score += 2  # 权重更高
    else: vqvae_score += 2
    
    if bar_result['normalized_entropy'] > vqvae_result['normalized_entropy']: bar_score += 2  # 权重更高
    else: vqvae_score += 2
    
    print(f"  BarTokenizer: {bar_score} 分")
    print(f"  CandlestickVQVAE: {vqvae_score} 分")
    
    winner = "BarTokenizer" if bar_score > vqvae_score else "CandlestickVQVAE"
    print(f"  🏆 综合优胜者: {winner}")


def main():
    """主测试函数"""
    print("BarTokenizer vs CandlestickVQVAETokenizer 全面性能对比")
    print("="*60)
    
    # 尝试加载真实数据
    real_data_paths = [
        "F:/hqdata/fut_top_min1.parquet",
        "F:/hqdata/fut_main_min5.parquet",
        "f:/featdata/fut_main_min5.parquet"
    ]
    
    df = None
    for path in real_data_paths:
        df = load_real_data(path, sample_size=2000)  # 减少样本量以加快测试
        if len(df) >= 1000:
            break
    
    if df is None or len(df) < 1000:
        print("使用模拟数据进行测试")
        df = create_realistic_data(2000)
    
    print(f"测试数据集: {len(df)} 条记录")
    
    # 运行全面对比
    results = comprehensive_comparison(df)
    
    # 打印详细结果
    print_detailed_comparison(results)
    
    print(f"\n🎯 主要发现:")
    print("1. 两种方法都成功完成了K线数据的token化")
    print("2. 在相同条件下的性能差异得到了量化")
    print("3. 真实数据验证了理论分析的正确性")
    print("4. 为实际应用提供了数据支撑的选择依据")


if __name__ == "__main__":
    main()
