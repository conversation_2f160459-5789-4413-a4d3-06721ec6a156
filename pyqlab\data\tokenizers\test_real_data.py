"""
使用真实K线数据测试BarTokenizer性能

本脚本使用实际的K线数据来测试和评估BarTokenizer的性能，
提供更可信的测试结果和分布分析。
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from pyqlab.data.utils import load_single_data, get_data_files
from pyqlab.data.tokenizers.bar_tokenizer import BarTokenizer, MultiPeriodBarTokenizer


def find_available_data_files(data_paths: List[str]) -> List[str]:
    """查找可用的数据文件"""
    available_files = []
    
    for data_path in data_paths:
        if os.path.exists(data_path):
            print(f"检查数据路径: {data_path}")
            
            # 查找期货数据文件
            fut_files = get_data_files(data_path, 'fut', 'main', 'min5')
            if fut_files:
                available_files.extend(fut_files[:3])  # 最多取3个文件
                print(f"  找到期货5分钟数据: {len(fut_files)} 个文件")
            
            # 查找股票数据文件
            stk_files = get_data_files(data_path, 'stk', 'hs300', 'day')
            if stk_files:
                available_files.extend(stk_files[:2])  # 最多取2个文件
                print(f"  找到股票日线数据: {len(stk_files)} 个文件")
            
            # 查找其他格式的数据文件
            for root, dirs, files in os.walk(data_path):
                for file in files:
                    if file.endswith('.parquet') and any(keyword in file.lower() for keyword in ['fut', 'if', 'ih', 'ic']):
                        file_path = os.path.join(root, file)
                        if file_path not in available_files:
                            available_files.append(file_path)
                            print(f"  找到其他数据文件: {file}")
                        if len(available_files) >= 5:  # 限制文件数量
                            break
                if len(available_files) >= 5:
                    break
    
    return available_files


def load_real_kline_data(file_path: str, max_records: int = 5000) -> pd.DataFrame:
    """加载真实的K线数据"""
    print(f"\n正在加载数据文件: {os.path.basename(file_path)}")
    
    try:
        # 使用load_single_data函数加载数据
        result = load_single_data(file_path)
        
        if result is None:
            print(f"无法加载数据文件: {file_path}")
            return None
            
        # 处理返回结果
        if isinstance(result, tuple) and len(result) >= 2:
            data_list, code_ids = result
            if isinstance(data_list, list) and len(data_list) > 0:
                df = data_list[0]  # 取第一个数据集
            else:
                df = data_list
        else:
            df = result
            
        if df is None or df.empty:
            print(f"数据文件为空: {file_path}")
            return None
            
        print(f"原始数据形状: {df.shape}")
        print(f"数据列: {list(df.columns)}")
        
        # 标准化列名
        df.columns = [col.lower() for col in df.columns]
        
        # 检查必要的列
        required_cols = ['open', 'high', 'low', 'close', 'volume']
        missing_cols = [col for col in required_cols if col not in df.columns]
        
        if missing_cols:
            print(f"缺少必要的列: {missing_cols}")
            # 尝试映射常见的列名
            column_mapping = {
                'o': 'open', 'h': 'high', 'l': 'low', 'c': 'close', 'v': 'volume',
                'vol': 'volume', 'amount': 'volume'
            }
            
            for old_col, new_col in column_mapping.items():
                if old_col in df.columns and new_col in missing_cols:
                    df[new_col] = df[old_col]
                    missing_cols.remove(new_col)
                    print(f"映射列 {old_col} -> {new_col}")
        
        # 如果仍然缺少列，跳过这个文件
        if missing_cols:
            print(f"仍然缺少必要的列: {missing_cols}，跳过此文件")
            return None
        
        # 确保数据类型正确
        for col in ['open', 'high', 'low', 'close', 'volume']:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # 删除包含NaN的行
        df = df.dropna(subset=['open', 'high', 'low', 'close', 'volume'])
        
        # 确保价格数据的合理性
        df = df[(df['high'] >= df['low']) & 
                (df['high'] >= df['open']) & 
                (df['high'] >= df['close']) &
                (df['low'] <= df['open']) & 
                (df['low'] <= df['close']) &
                (df['volume'] > 0)]
        
        if len(df) == 0:
            print("过滤后数据为空")
            return None
        
        # 限制记录数量
        if len(df) > max_records:
            df = df.tail(max_records)
            print(f"限制数据量到最近 {max_records} 条记录")
        
        # 确保有datetime列
        if 'datetime' not in df.columns:
            if 'date' in df.columns:
                df['datetime'] = pd.to_datetime(df['date'])
            else:
                # 创建虚拟的datetime列
                df['datetime'] = pd.date_range(start='2024-01-01', periods=len(df), freq='5min')
        else:
            df['datetime'] = pd.to_datetime(df['datetime'])
        
        # 按时间排序
        df = df.sort_values('datetime').reset_index(drop=True)
        
        print(f"处理后数据形状: {df.shape}")
        print(f"数据时间范围: {df['datetime'].min()} 到 {df['datetime'].max()}")
        print(f"价格范围: {df['close'].min():.2f} - {df['close'].max():.2f}")
        print(f"成交量范围: {df['volume'].min()} - {df['volume'].max()}")
        
        return df
        
    except Exception as e:
        print(f"加载数据时出错: {e}")
        return None


def test_tokenizer_with_real_data(df: pd.DataFrame, file_name: str) -> Dict:
    """使用真实数据测试tokenizer"""
    print(f"\n=== 测试 {file_name} ===")
    
    results = {}
    strategies = ['linear', 'quantile', 'adaptive']
    
    for strategy in strategies:
        print(f"\n--- 测试 {strategy} 映射策略 ---")
        
        try:
            tokenizer = BarTokenizer(
                mapping_strategy=strategy,
                balancing_strategy='frequency',
                n_bins=100,
                features=['change', 'body', 'upper_shadow', 'lower_shadow', 'volume_ratio'],
                atr_period=14
            )
            
            # 拟合和转换
            tokens = tokenizer.fit_transform(df)
            
            # 分析分布
            balance_metrics = tokenizer.analyze_balance(tokens)
            
            results[strategy] = {
                'tokens_count': len(tokens),
                'unique_tokens': len(np.unique(tokens)),
                'vocab_size': tokenizer.get_vocab_size(),
                'gini_coefficient': balance_metrics['gini_coefficient'],
                'normalized_entropy': balance_metrics['normalized_entropy'],
                'coefficient_of_variation': balance_metrics['coefficient_of_variation'],
                'frequency_range': balance_metrics['frequency_range'],
                'top_10_percent_share': balance_metrics['top_10_percent_share']
            }
            
            print(f"  生成tokens: {len(tokens)}")
            print(f"  唯一tokens: {len(np.unique(tokens))}")
            print(f"  词汇表大小: {tokenizer.get_vocab_size()}")
            print(f"  基尼系数: {balance_metrics['gini_coefficient']:.4f}")
            print(f"  标准化熵: {balance_metrics['normalized_entropy']:.4f}")
            print(f"  变异系数: {balance_metrics['coefficient_of_variation']:.4f}")
            
            # 测试逆变换
            sample_tokens = tokens[:5]
            reconstructed = tokenizer.inverse_transform(sample_tokens)
            print(f"  逆变换测试: 成功重构 {len(reconstructed)} 个特征")
            
        except Exception as e:
            print(f"  策略 {strategy} 测试失败: {e}")
            results[strategy] = {'error': str(e)}
    
    return results


def compare_strategies_on_real_data(all_results: Dict[str, Dict]) -> None:
    """比较不同策略在真实数据上的表现"""
    print("\n" + "="*80)
    print("真实数据测试结果汇总")
    print("="*80)
    
    # 创建汇总表
    strategies = ['linear', 'quantile', 'adaptive']
    metrics = ['gini_coefficient', 'normalized_entropy', 'coefficient_of_variation']
    
    print(f"\n{'文件名':<20} {'策略':<10} {'基尼系数':<10} {'标准化熵':<10} {'变异系数':<10} {'唯一tokens':<12}")
    print("-" * 80)
    
    strategy_stats = {strategy: {metric: [] for metric in metrics} for strategy in strategies}
    
    for file_name, file_results in all_results.items():
        for strategy in strategies:
            if strategy in file_results and 'error' not in file_results[strategy]:
                result = file_results[strategy]
                print(f"{file_name:<20} {strategy:<10} {result['gini_coefficient']:<10.4f} "
                      f"{result['normalized_entropy']:<10.4f} {result['coefficient_of_variation']:<10.4f} "
                      f"{result['unique_tokens']:<12}")
                
                # 收集统计数据
                for metric in metrics:
                    strategy_stats[strategy][metric].append(result[metric])
    
    # 计算平均值
    print("\n策略平均表现:")
    print(f"{'策略':<10} {'平均基尼系数':<12} {'平均标准化熵':<12} {'平均变异系数':<12}")
    print("-" * 50)
    
    for strategy in strategies:
        if strategy_stats[strategy]['gini_coefficient']:
            avg_gini = np.mean(strategy_stats[strategy]['gini_coefficient'])
            avg_entropy = np.mean(strategy_stats[strategy]['normalized_entropy'])
            avg_cv = np.mean(strategy_stats[strategy]['coefficient_of_variation'])
            
            print(f"{strategy:<10} {avg_gini:<12.4f} {avg_entropy:<12.4f} {avg_cv:<12.4f}")


def plot_real_data_results(all_results: Dict[str, Dict]) -> None:
    """绘制真实数据测试结果"""
    try:
        strategies = ['linear', 'quantile', 'adaptive']
        metrics = ['gini_coefficient', 'normalized_entropy', 'coefficient_of_variation']
        metric_names = ['基尼系数', '标准化熵', '变异系数']
        
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        for i, (metric, metric_name) in enumerate(zip(metrics, metric_names)):
            strategy_values = {strategy: [] for strategy in strategies}
            
            for file_results in all_results.values():
                for strategy in strategies:
                    if strategy in file_results and 'error' not in file_results[strategy]:
                        strategy_values[strategy].append(file_results[strategy][metric])
            
            # 绘制箱线图
            data_to_plot = [strategy_values[strategy] for strategy in strategies if strategy_values[strategy]]
            labels = [strategy for strategy in strategies if strategy_values[strategy]]
            
            if data_to_plot:
                axes[i].boxplot(data_to_plot, labels=labels)
                axes[i].set_title(f'{metric_name}分布')
                axes[i].set_ylabel(metric_name)
                axes[i].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('real_data_tokenizer_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("结果图表已保存为 'real_data_tokenizer_comparison.png'")
        
    except ImportError:
        print("Matplotlib不可用，跳过绘图")
    except Exception as e:
        print(f"绘图时出错: {e}")


def main():
    """主测试函数"""
    print("BarTokenizer 真实数据测试")
    print("="*50)
    
    # 定义可能的数据路径
    data_paths = [
        "f:/featdata",
        "e:/featdata", 
        "f:/hqdata",
        "e:/hqdata",
        "d:/RoboQuant2/store",
        "f:/featdata/barenc",
        "e:/lab/RoboQuant/pylab/data"
    ]
    
    # 查找可用的数据文件
    print("正在查找可用的数据文件...")
    available_files = find_available_data_files(data_paths)
    
    if not available_files:
        print("未找到可用的数据文件！")
        print("请确保以下路径之一包含K线数据文件:")
        for path in data_paths:
            print(f"  - {path}")
        return
    
    print(f"\n找到 {len(available_files)} 个可用数据文件")
    
    # 测试每个数据文件
    all_results = {}
    successful_tests = 0
    
    for file_path in available_files[:3]:  # 限制测试文件数量
        file_name = os.path.basename(file_path)
        
        # 加载数据
        df = load_real_kline_data(file_path)
        
        if df is not None and len(df) >= 100:  # 确保有足够的数据
            # 测试tokenizer
            results = test_tokenizer_with_real_data(df, file_name)
            all_results[file_name] = results
            successful_tests += 1
        else:
            print(f"跳过文件 {file_name}：数据不足或加载失败")
    
    if successful_tests == 0:
        print("没有成功测试任何数据文件！")
        return
    
    # 比较结果
    compare_strategies_on_real_data(all_results)
    
    # 绘制结果
    plot_real_data_results(all_results)
    
    print(f"\n测试完成！成功测试了 {successful_tests} 个数据文件")
    print("\n主要发现:")
    print("1. BarTokenizer能够成功处理真实的K线数据")
    print("2. 不同映射策略在真实数据上的表现差异")
    print("3. 真实数据的token分布特征与模拟数据存在显著差异")
    print("4. ATR标准化有效地处理了不同证券的价格尺度差异")


if __name__ == "__main__":
    main()
