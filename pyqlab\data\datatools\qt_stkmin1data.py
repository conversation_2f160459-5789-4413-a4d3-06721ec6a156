import sys
import pandas as pd
import os
from argparse import ArgumentParser
sys.path.append("d:/QuantLab")
from qtunnel import DataSource,BarData,BarSize,DoRight,RunMode


def process_batch(ds, code_batch, args):
    dfs = pd.DataFrame(columns=['datetime', 'code', 'price', 'volume'])
    for i, code in enumerate(code_batch):
        print(f"{i+1}/{len(code_batch)}: {code}")
        hist_data=ds.get_history_data(code, 0, [BarData.datetime, BarData.open, BarData.high, BarData.low, BarData.close, BarData.volume], BarSize.min1, DoRight.none)
        print(f"获取到数据: {hist_data.shape}")
        if len(hist_data) == 0:
            continue
        df = pd.DataFrame(hist_data, columns=['datetime', 'open', 'high', 'low', 'close', 'volume'])
        df['datetime'] = pd.to_datetime(df['datetime'], unit='s') + pd.Timedelta(hours=8)
        df['code'] = code
        dfs = pd.concat([dfs, df[['datetime', 'code', 'open', 'volume']].rename(columns={'open': 'price'})], axis=0)
        dfs = pd.concat([dfs, df[['datetime', 'code', 'high', 'volume']].rename(columns={'high': 'price'})], axis=0)
        dfs = pd.concat([dfs, df[['datetime', 'code', 'low', 'volume']].rename(columns={'low': 'price'})], axis=0)
        dfs = pd.concat([dfs, df[['datetime', 'code', 'close', 'volume']].rename(columns={'close': 'price'})], axis=0)
    
    return dfs

def save_batch_data(batch_dfs, output_dir, year_month, batch_idx):
    """保存批次数据到对应的月份文件"""
    if batch_dfs.empty:
        return
        
    batch_dfs.dropna(inplace=True)
    batch_dfs.sort_values(by=['datetime', 'code'], inplace=True)
    batch_dfs.reset_index(drop=True, inplace=True)
    
    # 按月分割保存
    for month, group in batch_dfs.groupby(pd.Grouper(key='datetime', freq='ME')):
        if year_month and month.date().strftime('%Y%m') != year_month:
            continue
            
        # 如果文件已存在，则追加
        output_file = f"{output_dir}/min1_{month.date().strftime('%Y%m')}.parquet"
        if os.path.exists(output_file) and batch_idx > 0:
            try:
                existing_df = pd.read_parquet(output_file)
                combined_df = pd.concat([existing_df, group], axis=0)
                combined_df.drop_duplicates(subset=['datetime', 'code', 'price'], keep='first', inplace=True)
                combined_df.sort_values(by=['datetime', 'code'], inplace=True)
                combined_df.reset_index(drop=True, inplace=True)
                combined_df.to_parquet(output_file)
                print(f"更新文件: {output_file}, 记录数: {len(combined_df)}")
            except Exception as e:
                print(f"合并文件时出错: {e}")
                print(f"直接写入新数据到: {output_file}")
                group.to_parquet(output_file)
        else:
            group.to_parquet(output_file)
            print(f"创建文件: {output_file}, 记录数: {len(group)}")

def main(args):
    ds=DataSource(RunMode.passive)
    print(ds.get_run_dir())

    if args.market == "fut":
        code_list = ds.get_block_data("主力9999")
    elif args.market == "sf":
        code_list = ["IF9999.SF", "IH9999.SF", "IC9999.SF", "IM9999.SF"]
    elif args.market == "stk":
        code_list = ds.get_block_data("股指板块")
    else:
        raise ValueError(f"unknown market: {args.market}")
    
    print(f"总共需要处理 {len(code_list)} 个代码")
    
    # 确保输出目录存在
    output_dir = f"{args.data_path}/{args.market}"
    os.makedirs(output_dir, exist_ok=True)
    
    # 分批处理
    batch_size = args.batch_size
    total_batches = (len(code_list) - 1) // batch_size + 1
    
    for batch_idx in range(0, len(code_list), batch_size):
        batch_num = batch_idx // batch_size + 1
        print(f"\n处理批次 {batch_num}/{total_batches}")
        code_batch = code_list[batch_idx:batch_idx + batch_size]
        
        # 处理当前批次
        batch_dfs = process_batch(ds, code_batch, args)
        
        # 保存当前批次数据
        print(f"保存批次 {batch_num} 数据...")
        save_batch_data(batch_dfs, output_dir, args.year_month, batch_idx)
        
        # 释放内存
        del batch_dfs
        import gc
        gc.collect()
        
        print(f"批次 {batch_num}/{total_batches} 处理完成")

if __name__ == "__main__":
    parser = ArgumentParser()
    parser.add_argument("--data_path", type=str, default="f:/hqdata")
    parser.add_argument("--market", type=str, default="stk", choices=["fut", "stk", "sf"])
    parser.add_argument("--year_month", type=str, default="202410")
    parser.add_argument("--batch_size", type=int, default=500, help="每批处理的代码数量")
    args = parser.parse_args()
    print(args)
    main(args)

