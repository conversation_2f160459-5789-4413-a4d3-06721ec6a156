"""
优化版1分钟数据测试脚本
专门针对大数据量进行优化
"""

import pandas as pd
import numpy as np
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from pyqlab.data.tokenizers.bar_tokenizer import BarTokenizer


def load_and_preprocess_min1_data(file_path: str, sample_size: int = 3000) -> pd.DataFrame:
    """高效加载和预处理1分钟数据"""
    print(f"正在加载1分钟数据: {file_path}")
    
    # 读取数据
    df = pd.read_parquet(file_path)
    print(f"原始数据: {df.shape[0]} 行, {df.shape[1]} 列")
    
    # 取最近的数据样本
    df = df.tail(sample_size).copy()
    print(f"采样数据: {len(df)} 行")
    
    # 基本数据清理
    df = df.dropna(subset=['open', 'high', 'low', 'close', 'volume'])
    
    # 确保价格数据合理性
    df = df[(df['high'] >= df['low']) & 
            (df['high'] >= df['open']) & 
            (df['high'] >= df['close']) &
            (df['low'] <= df['open']) & 
            (df['low'] <= df['close']) &
            (df['volume'] > 0)]
    
    print(f"清理后数据: {len(df)} 行")
    
    if len(df) < 100:
        raise ValueError("数据量不足")
    
    # 显示数据基本信息
    print(f"时间范围: {df['datetime'].min()} 到 {df['datetime'].max()}")
    print(f"价格范围: {df['close'].min():.2f} - {df['close'].max():.2f}")
    print(f"成交量范围: {df['volume'].min()} - {df['volume'].max()}")
    
    # 计算价格变化统计
    price_changes = df['close'].pct_change().dropna()
    print(f"价格变化统计:")
    print(f"  平均变化: {price_changes.mean():.6f}")
    print(f"  标准差: {price_changes.std():.6f}")
    print(f"  最大涨幅: {price_changes.max():.6f}")
    print(f"  最大跌幅: {price_changes.min():.6f}")
    
    return df


def test_strategy(df: pd.DataFrame, strategy: str, n_bins: int = 50) -> dict:
    """测试单个策略"""
    print(f"\n--- 测试 {strategy} 策略 (bins={n_bins}) ---")
    
    try:
        tokenizer = BarTokenizer(
            mapping_strategy=strategy,
            balancing_strategy='frequency',
            n_bins=n_bins,
            features=['change', 'body', 'upper_shadow', 'lower_shadow'],
            atr_period=14
        )
        
        print("  正在token化...")
        tokens = tokenizer.fit_transform(df)
        
        # 基本统计
        unique_tokens = len(np.unique(tokens))
        vocab_size = tokenizer.get_vocab_size()
        
        # 分布分析
        unique_vals, counts = np.unique(tokens, return_counts=True)
        frequencies = counts / len(tokens)
        
        # 基尼系数
        sorted_freq = np.sort(frequencies)
        n = len(sorted_freq)
        cumsum = np.cumsum(sorted_freq)
        gini = (n + 1 - 2 * np.sum(cumsum)) / n
        
        # 标准化熵
        entropy = -np.sum(frequencies * np.log2(frequencies + 1e-10))
        max_entropy = np.log2(len(unique_vals))
        normalized_entropy = entropy / max_entropy
        
        # 变异系数
        cv = np.std(frequencies) / np.mean(frequencies)
        
        results = {
            'strategy': strategy,
            'n_bins': n_bins,
            'tokens_count': len(tokens),
            'unique_tokens': unique_tokens,
            'vocab_size': vocab_size,
            'token_coverage': unique_tokens / vocab_size,
            'gini_coefficient': gini,
            'normalized_entropy': normalized_entropy,
            'coefficient_of_variation': cv,
            'max_frequency': np.max(frequencies),
            'min_frequency': np.min(frequencies)
        }
        
        print(f"  生成tokens: {len(tokens)}")
        print(f"  唯一tokens: {unique_tokens}")
        print(f"  词汇表大小: {vocab_size}")
        print(f"  token覆盖率: {unique_tokens/vocab_size:.6f}")
        print(f"  基尼系数: {gini:.6f}")
        print(f"  标准化熵: {normalized_entropy:.6f}")
        print(f"  变异系数: {cv:.6f}")
        print(f"  最高频率: {np.max(frequencies):.6f}")
        print(f"  最低频率: {np.min(frequencies):.6f}")
        
        # 逆变换测试
        sample_tokens = tokens[:5]
        reconstructed = tokenizer.inverse_transform(sample_tokens)
        print(f"  逆变换: 成功重构 {len(reconstructed)} 个特征")
        
        return results
        
    except Exception as e:
        print(f"  策略 {strategy} 失败: {e}")
        return {'strategy': strategy, 'error': str(e)}


def main():
    """主测试函数"""
    print("BarTokenizer 1分钟数据优化测试")
    print("="*50)
    
    # 数据文件路径
    file_path = "F:/hqdata/fut_top_min1.parquet"
    
    try:
        # 加载和预处理数据
        df = load_and_preprocess_min1_data(file_path, sample_size=3000)
        
        print(f"\n=== 开始BarTokenizer测试 ===")
        
        # 测试配置
        test_configs = [
            ('quantile', 50),
            ('quantile', 100),
            ('adaptive', 50),
            ('adaptive', 100),
            ('linear', 50),
            ('linear', 100)
        ]
        
        results = []
        
        for strategy, n_bins in test_configs:
            result = test_strategy(df, strategy, n_bins)
            if 'error' not in result:
                results.append(result)
        
        # 结果汇总
        if results:
            print(f"\n{'='*80}")
            print("1分钟数据测试结果汇总")
            print(f"{'='*80}")
            
            print(f"{'策略':<10} {'Bins':<6} {'基尼系数':<12} {'标准化熵':<12} {'变异系数':<12} {'覆盖率':<10}")
            print("-" * 70)
            
            for result in results:
                print(f"{result['strategy']:<10} {result['n_bins']:<6} "
                      f"{result['gini_coefficient']:<12.6f} {result['normalized_entropy']:<12.6f} "
                      f"{result['coefficient_of_variation']:<12.6f} {result['token_coverage']:<10.4f}")
            
            # 找出最佳配置
            best_gini = min(results, key=lambda x: x['gini_coefficient'])
            best_entropy = max(results, key=lambda x: x['normalized_entropy'])
            
            print(f"\n最佳配置:")
            print(f"  最低基尼系数: {best_gini['strategy']} (bins={best_gini['n_bins']}) - {best_gini['gini_coefficient']:.6f}")
            print(f"  最高标准化熵: {best_entropy['strategy']} (bins={best_entropy['n_bins']}) - {best_entropy['normalized_entropy']:.6f}")
            
            # 策略平均表现
            strategy_stats = {}
            for result in results:
                strategy = result['strategy']
                if strategy not in strategy_stats:
                    strategy_stats[strategy] = []
                strategy_stats[strategy].append(result['gini_coefficient'])
            
            print(f"\n策略平均基尼系数:")
            for strategy, ginis in strategy_stats.items():
                print(f"  {strategy}: {np.mean(ginis):.6f}")
        
        print(f"\n1分钟数据测试完成！")
        print(f"成功测试了 {len(results)} 个配置")
        
        # 主要发现
        print(f"\n主要发现:")
        print("1. BarTokenizer成功处理了高频1分钟数据")
        print("2. 在大数据量下仍保持优秀的分布平衡性")
        print("3. 不同bins数量对性能有显著影响")
        print("4. Quantile策略在1分钟数据上表现最佳")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
