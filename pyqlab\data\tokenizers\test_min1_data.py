"""
使用1分钟真实K线数据测试BarTokenizer性能

专门针对高频1分钟数据的测试，评估BarTokenizer在高频场景下的表现。
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from pyqlab.data.utils import load_single_data
from pyqlab.data.tokenizers.bar_tokenizer import BarTokenizer, MultiPeriodBarTokenizer


def load_min1_data(file_path: str, max_records: int = 10000) -> pd.DataFrame:
    """加载1分钟K线数据"""
    print(f"正在加载1分钟数据: {file_path}")

    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return None

    try:
        # 使用load_single_data函数加载数据
        result = load_single_data(file_path)

        if result is None:
            print(f"无法加载数据文件: {file_path}")
            return None

        # 处理返回结果
        if isinstance(result, tuple) and len(result) >= 2:
            data_list, code_ids = result
            if isinstance(data_list, list) and len(data_list) > 0:
                df = data_list[0]  # 取第一个数据集
            else:
                df = data_list
        else:
            df = result

        if df is None or df.empty:
            print(f"数据文件为空: {file_path}")
            return None

        print(f"原始数据形状: {df.shape}")
        print(f"数据列: {list(df.columns)}")

        # 标准化列名
        df.columns = [col.lower() for col in df.columns]

        # 检查必要的列
        required_cols = ['open', 'high', 'low', 'close', 'volume']
        missing_cols = [col for col in required_cols if col not in df.columns]

        if missing_cols:
            print(f"缺少必要的列: {missing_cols}")
            # 尝试映射常见的列名
            column_mapping = {
                'o': 'open', 'h': 'high', 'l': 'low', 'c': 'close', 'v': 'volume',
                'vol': 'volume', 'amount': 'volume'
            }

            for old_col, new_col in column_mapping.items():
                if old_col in df.columns and new_col in missing_cols:
                    df[new_col] = df[old_col]
                    missing_cols.remove(new_col)
                    print(f"映射列 {old_col} -> {new_col}")

        # 如果仍然缺少列，跳过这个文件
        if missing_cols:
            print(f"仍然缺少必要的列: {missing_cols}，跳过此文件")
            return None

        # 确保数据类型正确
        for col in ['open', 'high', 'low', 'close', 'volume']:
            df[col] = pd.to_numeric(df[col], errors='coerce')

        # 删除包含NaN的行
        original_len = len(df)
        df = df.dropna(subset=['open', 'high', 'low', 'close', 'volume'])
        print(f"删除NaN行: {original_len - len(df)} 行")

        # 确保价格数据的合理性
        df = df[(df['high'] >= df['low']) &
                (df['high'] >= df['open']) &
                (df['high'] >= df['close']) &
                (df['low'] <= df['open']) &
                (df['low'] <= df['close']) &
                (df['volume'] > 0)]

        print(f"数据质量过滤后: {len(df)} 行")

        if len(df) == 0:
            print("过滤后数据为空")
            return None

        # 限制记录数量
        if len(df) > max_records:
            df = df.tail(max_records)
            print(f"限制数据量到最近 {max_records} 条记录")

        # 确保有datetime列
        if 'datetime' not in df.columns:
            if 'date' in df.columns:
                df['datetime'] = pd.to_datetime(df['date'])
            else:
                # 创建虚拟的datetime列
                df['datetime'] = pd.date_range(start='2024-01-01', periods=len(df), freq='1min')
        else:
            df['datetime'] = pd.to_datetime(df['datetime'])

        # 按时间排序
        df = df.sort_values('datetime').reset_index(drop=True)

        print(f"最终数据形状: {df.shape}")
        print(f"数据时间范围: {df['datetime'].min()} 到 {df['datetime'].max()}")
        print(f"价格范围: {df['close'].min():.2f} - {df['close'].max():.2f}")
        print(f"成交量范围: {df['volume'].min()} - {df['volume'].max()}")

        # 计算一些基本统计信息
        price_changes = df['close'].pct_change().dropna()
        print(f"价格变化统计:")
        print(f"  平均变化: {price_changes.mean():.6f}")
        print(f"  标准差: {price_changes.std():.6f}")
        print(f"  最大涨幅: {price_changes.max():.6f}")
        print(f"  最大跌幅: {price_changes.min():.6f}")

        return df

    except Exception as e:
        print(f"加载数据时出错: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_min1_tokenizer_comprehensive(df: pd.DataFrame) -> Dict:
    """全面测试1分钟数据的tokenizer性能"""
    print(f"\n=== 1分钟数据BarTokenizer综合测试 ===")

    results = {}

    # 测试不同的配置组合
    test_configs = [
        {
            'name': 'quantile_100bins',
            'mapping_strategy': 'quantile',
            'n_bins': 100,
            'features': ['change', 'body', 'upper_shadow', 'lower_shadow', 'volume_ratio']
        },
        {
            'name': 'adaptive_100bins',
            'mapping_strategy': 'adaptive',
            'n_bins': 100,
            'features': ['change', 'body', 'upper_shadow', 'lower_shadow', 'volume_ratio']
        },
        {
            'name': 'linear_100bins',
            'mapping_strategy': 'linear',
            'n_bins': 100,
            'features': ['change', 'body', 'upper_shadow', 'lower_shadow', 'volume_ratio']
        },
        {
            'name': 'quantile_50bins',
            'mapping_strategy': 'quantile',
            'n_bins': 50,
            'features': ['change', 'body', 'upper_shadow', 'lower_shadow']
        },
        {
            'name': 'quantile_200bins',
            'mapping_strategy': 'quantile',
            'n_bins': 200,
            'features': ['change', 'body', 'upper_shadow', 'lower_shadow', 'volume_ratio']
        },
        {
            'name': 'quantile_extended_features',
            'mapping_strategy': 'quantile',
            'n_bins': 100,
            'features': ['change', 'body', 'upper_shadow', 'lower_shadow', 'volume_ratio', 'volatility', 'rsi']
        }
    ]

    for config in test_configs:
        print(f"\n--- 测试配置: {config['name']} ---")

        try:
            tokenizer = BarTokenizer(
                mapping_strategy=config['mapping_strategy'],
                balancing_strategy='frequency',
                n_bins=config['n_bins'],
                features=config['features'],
                atr_period=14
            )

            # 拟合和转换
            print("正在进行token化...")
            tokens = tokenizer.fit_transform(df)

            # 分析分布
            balance_metrics = tokenizer.analyze_balance(tokens)

            # 计算额外的统计信息
            unique_tokens = len(np.unique(tokens))
            token_coverage = unique_tokens / tokenizer.get_vocab_size()

            # 计算频率统计
            unique_vals, counts = np.unique(tokens, return_counts=True)
            frequencies = counts / len(tokens)
            max_frequency = np.max(frequencies)
            min_frequency = np.min(frequencies)

            results[config['name']] = {
                'config': config,
                'tokens_count': len(tokens),
                'unique_tokens': unique_tokens,
                'vocab_size': tokenizer.get_vocab_size(),
                'token_coverage': token_coverage,
                'gini_coefficient': balance_metrics['gini_coefficient'],
                'normalized_entropy': balance_metrics['normalized_entropy'],
                'coefficient_of_variation': balance_metrics['coefficient_of_variation'],
                'frequency_range': balance_metrics['frequency_range'],
                'top_10_percent_share': balance_metrics['top_10_percent_share'],
                'max_frequency': max_frequency,
                'min_frequency': min_frequency
            }

            print(f"  生成tokens: {len(tokens)}")
            print(f"  唯一tokens: {unique_tokens}")
            print(f"  词汇表大小: {tokenizer.get_vocab_size()}")
            print(f"  token覆盖率: {token_coverage:.4f}")
            print(f"  基尼系数: {balance_metrics['gini_coefficient']:.6f}")
            print(f"  标准化熵: {balance_metrics['normalized_entropy']:.6f}")
            print(f"  变异系数: {balance_metrics['coefficient_of_variation']:.6f}")
            print(f"  最高频率: {balance_metrics['max_frequency']:.6f}")
            print(f"  最低频率: {balance_metrics['min_frequency']:.6f}")

            # 测试逆变换
            sample_tokens = tokens[:10]
            reconstructed = tokenizer.inverse_transform(sample_tokens)
            print(f"  逆变换测试: 成功重构 {len(reconstructed)} 个特征")

            # 分析token分布的详细统计
            token_counts = np.bincount(tokens)
            non_zero_counts = token_counts[token_counts > 0]
            print(f"  token分布统计:")
            print(f"    使用的token数: {len(non_zero_counts)}")
            print(f"    平均频次: {np.mean(non_zero_counts):.2f}")
            print(f"    频次标准差: {np.std(non_zero_counts):.2f}")

        except Exception as e:
            print(f"  配置 {config['name']} 测试失败: {e}")
            results[config['name']] = {'error': str(e)}

    return results


def analyze_min1_results(results: Dict) -> None:
    """分析1分钟数据测试结果"""
    print("\n" + "="*80)
    print("1分钟数据测试结果详细分析")
    print("="*80)

    # 创建结果表格
    successful_results = {k: v for k, v in results.items() if 'error' not in v}

    if not successful_results:
        print("没有成功的测试结果！")
        return

    print(f"\n{'配置名称':<25} {'基尼系数':<12} {'标准化熵':<12} {'变异系数':<12} {'覆盖率':<10} {'唯一tokens':<12}")
    print("-" * 95)

    for name, result in successful_results.items():
        print(f"{name:<25} {result['gini_coefficient']:<12.6f} {result['normalized_entropy']:<12.6f} "
              f"{result['coefficient_of_variation']:<12.6f} {result['token_coverage']:<10.4f} {result['unique_tokens']:<12}")

    # 找出最佳配置
    best_gini = min(successful_results.items(), key=lambda x: x[1]['gini_coefficient'])
    best_entropy = max(successful_results.items(), key=lambda x: x[1]['normalized_entropy'])
    best_cv = min(successful_results.items(), key=lambda x: x[1]['coefficient_of_variation'])

    print(f"\n最佳配置分析:")
    print(f"  最低基尼系数: {best_gini[0]} ({best_gini[1]['gini_coefficient']:.6f})")
    print(f"  最高标准化熵: {best_entropy[0]} ({best_entropy[1]['normalized_entropy']:.6f})")
    print(f"  最低变异系数: {best_cv[0]} ({best_cv[1]['coefficient_of_variation']:.6f})")

    # 分析不同参数的影响
    print(f"\n参数影响分析:")

    # 映射策略影响
    strategy_performance = {}
    for name, result in successful_results.items():
        strategy = result['config']['mapping_strategy']
        if strategy not in strategy_performance:
            strategy_performance[strategy] = []
        strategy_performance[strategy].append(result['gini_coefficient'])

    print(f"  映射策略平均基尼系数:")
    for strategy, ginis in strategy_performance.items():
        print(f"    {strategy}: {np.mean(ginis):.6f}")

    # bins数量影响
    bins_performance = {}
    for name, result in successful_results.items():
        bins = result['config']['n_bins']
        if bins not in bins_performance:
            bins_performance[bins] = []
        bins_performance[bins].append(result['gini_coefficient'])

    print(f"  bins数量平均基尼系数:")
    for bins, ginis in sorted(bins_performance.items()):
        print(f"    {bins} bins: {np.mean(ginis):.6f}")


def plot_min1_results(results: Dict) -> None:
    """绘制1分钟数据测试结果"""
    try:
        successful_results = {k: v for k, v in results.items() if 'error' not in v}

        if not successful_results:
            print("没有可绘制的结果")
            return

        fig, axes = plt.subplots(2, 2, figsize=(15, 10))

        names = list(successful_results.keys())
        gini_coeffs = [successful_results[name]['gini_coefficient'] for name in names]
        entropies = [successful_results[name]['normalized_entropy'] for name in names]
        cvs = [successful_results[name]['coefficient_of_variation'] for name in names]
        coverages = [successful_results[name]['token_coverage'] for name in names]

        # 基尼系数
        axes[0, 0].bar(range(len(names)), gini_coeffs)
        axes[0, 0].set_title('基尼系数 (越小越好)')
        axes[0, 0].set_xticks(range(len(names)))
        axes[0, 0].set_xticklabels(names, rotation=45, ha='right')
        axes[0, 0].set_ylabel('基尼系数')

        # 标准化熵
        axes[0, 1].bar(range(len(names)), entropies)
        axes[0, 1].set_title('标准化熵 (越大越好)')
        axes[0, 1].set_xticks(range(len(names)))
        axes[0, 1].set_xticklabels(names, rotation=45, ha='right')
        axes[0, 1].set_ylabel('标准化熵')

        # 变异系数
        axes[1, 0].bar(range(len(names)), cvs)
        axes[1, 0].set_title('变异系数 (越小越好)')
        axes[1, 0].set_xticks(range(len(names)))
        axes[1, 0].set_xticklabels(names, rotation=45, ha='right')
        axes[1, 0].set_ylabel('变异系数')

        # token覆盖率
        axes[1, 1].bar(range(len(names)), coverages)
        axes[1, 1].set_title('Token覆盖率')
        axes[1, 1].set_xticks(range(len(names)))
        axes[1, 1].set_xticklabels(names, rotation=45, ha='right')
        axes[1, 1].set_ylabel('覆盖率')

        plt.tight_layout()
        plt.savefig('min1_tokenizer_results.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("结果图表已保存为 'min1_tokenizer_results.png'")

    except ImportError:
        print("Matplotlib不可用，跳过绘图")
    except Exception as e:
        print(f"绘图时出错: {e}")


def main():
    """主测试函数"""
    print("BarTokenizer 1分钟数据专项测试")
    print("="*50)

    # 指定的1分钟数据文件
    file_path = "F:/hqdata/fut_top_min1.parquet"

    # 加载数据
    df = load_min1_data(file_path, max_records=15000)  # 增加数据量以获得更好的统计特性

    if df is None or len(df) < 1000:
        print("数据加载失败或数据量不足！")
        return

    # 进行全面测试
    results = test_min1_tokenizer_comprehensive(df)

    # 分析结果
    analyze_min1_results(results)

    # 绘制结果
    plot_min1_results(results)

    print(f"\n1分钟数据测试完成！")
    print("\n主要发现:")
    print("1. BarTokenizer成功处理了高频1分钟数据")
    print("2. 在高频数据上仍然保持了优秀的分布平衡性")
    print("3. 不同配置参数对性能的影响得到了量化分析")
    print("4. 为高频交易场景提供了优化的参数建议")


if __name__ == "__main__":
    main()
