# PyQLab Data 模块

PyQLab Data 模块是一个专业的金融数据处理和机器学习数据集构建框架，专注于证券市场数据的获取、存储、处理和模型训练数据准备。

## 🎯 核心功能

### 1. 数据获取与存储
- **多源数据获取**：支持通过 TDX（通达信）等第三方行情源获取历史行情数据
- **时序数据库**：基于 Parquet 格式的高效时序数据存储系统
- **增量更新**：支持历史数据的定期增量更新
- **数据类型**：
  - 股票和期货的日线、5分钟、1分钟历史行情数据
  - Tick 级别的分笔成交数据
  - 基本面数据（财务数据、行业数据、公司信息等）

### 2. 数据处理与特征工程
- **K线数据标准化**：将原始K线数据转换为标准化的特征表示
- **技术指标计算**：内置丰富的技术分析指标
- **时间特征编码**：支持多种时间特征编码方式
- **数据质量优化**：数据清洗、异常值处理、样本平衡等

### 3. 机器学习数据集
- **BarDataset**：K线数据的 Token 化数据集，支持 Transformer 模型训练
- **FTSDataset**：因子时序数据集，支持多种模型架构
- **Pipeline**：数据处理管道，统一数据预处理流程

## 📁 目录结构

```
pyqlab/data/
├── __init__.py                 # 模块初始化
├── data_api.py                # 数据API接口
├── tsdb.py                    # 时序数据库核心类
├── tdxhq.py                   # TDX行情数据获取
├── orderbook_collector.py     # 订单簿数据收集
├── orderbook_processor.py     # 订单簿数据处理
├── dataset/                   # 数据集模块
│   ├── dataset_bar.py         # K线数据集
│   ├── dataset_fts.py         # 因子时序数据集
│   ├── handler.py             # 数据处理器
│   ├── loader.py              # 数据加载器
│   ├── pipeline.py            # 数据处理管道
│   └── utils.py               # 工具函数
├── datatools/                 # 数据工具
│   ├── factors_etl.py         # 因子ETL处理
│   ├── customindictor.py      # 自定义技术指标
│   ├── crosssection.py        # 横截面数据处理
│   └── settlementdata.py      # 结算数据处理
├── cmd/                       # 批处理脚本
│   ├── export_tdx_to_db.bat   # TDX数据导出
│   ├── export_tdx_barenc.bat  # K线编码导出
│   └── optimize_data_quality.bat # 数据质量优化
└── tests/                     # 单元测试
    ├── test_dataset_bar.py    # BarDataset测试
    ├── test_dataset_fts.py    # FTSDataset测试
    └── test_pipeline.py       # Pipeline测试
```

## 🚀 快速开始

### 1. 基本使用

```python
from pyqlab.data import get_dataset, BarDataset, FTSDataset

# 获取因子时序数据集
dataset = get_dataset(
    ds_files=['2024'],
    fut_codes=['IF', 'IH', 'IC'],
    data_path="f:/featdata",
    model_type=1,
    seq_len=32,
    pred_len=1
)

# 加载数据
dataset.load_data()
```

### 2. 时序数据库使用

```python
from pyqlab.data.tsdb import TimeSeriesDB

# 初始化数据库
tsdb = TimeSeriesDB("f:/hqdata/tsdb")

# 写入行情数据
tsdb.write_market_data(
    symbol="IF2312",
    data=df,
    market="CN",
    data_type="bar",
    freq="min5"
)

# 读取行情数据
data = tsdb.read_market_data(
    symbol="IF2312",
    start_date="2024-01-01",
    end_date="2024-12-31",
    freq="min5"
)
```

## 📊 数据集详解

### BarDataset - K线Token数据集

BarDataset 将证券的K线时序数据离散化，转换为适合大语言模型训练的 tokens。

**核心特性：**
- **K线组件标准化**：将 change、entity、upline、downline 四个标量标准化
- **Token化处理**：基于ATR值进行归一化，生成约40,000大小的词汇表
- **时间特征编码**：支持多种时间编码方式（整数编码、周期编码等）
- **间隔Token**：处理交易日间隔，帮助模型识别市场开闭市

**数据格式：**
```python
# 输入：原始K线数据
# 输出：(code, x, x_mark, y, y_mark)
# - code: 证券代码编码
# - x: K线bar token序列
# - x_mark: 时间特征标记
# - y: 目标bar token
# - y_mark: 目标时间特征
```

**使用示例：**
```python
from pyqlab.data.dataset.dataset_bar import BarDataset
from pyqlab.data.dataset.pipeline import Pipeline

# 配置数据集
config = BarDataset.get_default_config()
config.data_path = 'f:/featdata/barenc/db2'
config.market = 'fut'
config.period = 'min5'
config.block_size = 15

# 加载数据
pipeline = Pipeline(
    data_path=config.data_path,
    market=config.market,
    block_name='sf',
    period=config.period,
    start_year=2024,
    end_year=2024
)
data = pipeline.get_data()

# 创建数据集
dataset = BarDataset(config, data)
```

### FTSDataset - 因子时序数据集

FTSDataset 提供基于技术指标因子的时序数据集，支持多种模型架构。

**支持的模型类型：**
1. **model_type=0**: MLP、CNN、TCN等传统模型
2. **model_type=1**: Transformer、GPT等序列模型

**因子类型：**
- **长周期因子**：MACD、RSI、动量指标等
- **短周期因子**：快速RSI、短期动量等
- **上下文因子**：时间特征、市场环境因子
- **技术指标**：趋势、波动率、支撑阻力等

**使用示例：**
```python
from pyqlab.data import get_dataset

# 获取数据集
dataset = get_dataset(
    ds_files=['2024'],
    ins_nums=(0, 51, 51, 17),  # (长因子数, 短因子数, 主因子数, 上下文因子数)
    fut_codes=['IF', 'IH', 'IC', 'IM'],
    model_type=1,
    seq_len=20,
    pred_len=1
)

# 设置数据
dataset.setup_data()
dataset.load_data()

# 获取样本
sample = dataset[0]  # (emb, x_data, x_mark, y_data, y_mark)
```

## 🔧 数据处理管道

### Pipeline 类

Pipeline 是数据处理的核心类，负责数据加载、预处理和特征工程。

**主要功能：**
- **数据加载**：从多种格式加载历史数据
- **时间特征编码**：支持3种编码方式
  - `timeenc=0`: 整数编码（月、日、星期、小时、分钟）
  - `timeenc=1`: 标准化时间特征
  - `timeenc=2`: 正弦余弦周期编码
- **Bar Token化**：将K线数据转换为离散token
- **数据过滤**：时间范围过滤、代码筛选等

**时间编码示例：**
```python
# timeenc=2: 周期编码（推荐用于深度学习）
time_features = [
    'hour_sin', 'hour_cos',      # 小时周期
    'dow_sin', 'dow_cos',        # 星期周期
    'month_sin', 'month_cos',    # 月份周期
    'doy_sin', 'doy_cos'         # 年内日期周期
]
```

## 💾 时序数据库 (TimeSeriesDB)

基于 Parquet 格式的高性能时序数据库，专为金融数据优化。

**存储结构：**
```
db_path/
├── market/                    # 行情数据
│   ├── tick/                  # Tick数据
│   │   └── YYYY/MM/DD/        # 按日期组织
│   └── bar/                   # K线数据
│       ├── min1/YYYY/MM/      # 1分钟数据按月
│       ├── min5/YYYY/         # 5分钟数据按年
│       └── day/YYYY/          # 日线数据按年
├── fundamental/               # 基本面数据
│   ├── basic/                 # 基础信息
│   ├── financial/YYYY/QX/     # 财务数据按季度
│   └── industry/              # 行业数据
└── metadata/                  # 元数据
    ├── markets.parquet        # 市场信息
    ├── symbols.parquet        # 证券信息
    └── instruments.parquet    # 合约信息
```

**核心方法：**
```python
# 写入数据
tsdb.write_market_data(symbol, data, market, data_type, freq)
tsdb.write_fundamental_data(symbol, data, market, data_type, date)

# 读取数据
tsdb.read_market_data(symbol, start_date, end_date, market, data_type, freq)
tsdb.read_fundamental_data(symbol, market, data_type, date)

# 管理证券
tsdb.add_symbol(symbol, name, market, industry, ...)
tsdb.list_symbols(market, industry, active_only)
```

## 📈 数据获取 (TdxExHq)

通过 TDX（通达信）接口获取实时和历史行情数据。

**支持的数据类型：**
- **历史K线**：日线、5分钟、1分钟
- **历史Tick**：分笔成交数据
- **市场覆盖**：股票、商品期货、金融期货

**使用示例：**
```python
from pyqlab.data.tdxhq import TdxExHq

# 初始化连接
tdx = TdxExHq()
tdx.connect_hq_server('120.79.210.76', 7722)

# 获取期货历史数据
df = tdx.get_fut_history_data(
    market='SF',
    code='IF',
    period='min5',
    start_date=20240101
)

# 更新历史数据
tdx.update_fut_history_data(
    start_date=20240101,
    block_name='main',
    period='min5',
    rq_path='f:/hqdata'
)
```

## 🧪 测试

运行测试套件：

```bash
# 运行所有测试
python -m pyqlab.data.tests.run_tests

# 运行特定测试
python -m pyqlab.data.tests.test_dataset_bar
python -m pyqlab.data.tests.test_dataset_fts
python -m pyqlab.data.tests.test_pipeline
```

## ⚙️ 配置说明

### 数据API配置

```python
# 因子选择
SEL_LONG_FACTOR_NAMES = [    # 长周期因子
    "NEW_CHANGE_PERCENT", "MACD", "RSI",
    "LR_SLOPE_FAST", "MOMENTUM", "BAND_POSITION"
]

SEL_SHORT_FACTOR_NAMES = [   # 短周期因子
    "FAST_RSI", "FAST_NATR", "FAST_MOM"
]

# 数据处理配置
data_handler_config = {
    "years": ['2024'],
    "kwargs": {
        "win": 20,                    # 序列长度
        "step": 1,                    # 采样步长
        "is_normal": True,            # 是否归一化
        "timeenc": 2,                 # 时间编码方式
        "ins_nums": (0, 51, 51, 17)   # 各类因子数量
    }
}
```

## 🔄 数据处理流程

### 1. 数据获取流程
```bash
# 1. 获取TDX历史数据
python tdxhq.py --update_history --market fut --period min5 --block_name main

# 2. 导出到时序数据库
python cmd/export_tdx_to_db.bat

# 3. 数据质量优化
python cmd/optimize_data_quality.bat
```

### 2. 特征工程流程
```python
# 1. 加载原始数据
pipeline = Pipeline(data_path, market, block_name, period, start_year, end_year)

# 2. 数据预处理
df = pipeline._load_data()
df = pipeline._prepare_data()

# 3. 特征编码
df = pipeline._to_time_tf(df)
df = Pipeline.to_bar(df)  # K线Token化

# 4. 创建数据集
dataset = BarDataset(config, df)
```

## 📋 最佳实践

### 1. 数据存储
- 使用 Parquet 格式存储，压缩比高，读取速度快
- 按时间分区存储，便于增量更新和范围查询
- 定期备份重要数据

### 2. 特征工程
- 使用标准化的时间编码，提高模型泛化能力
- 合理设置序列长度，平衡信息量和计算效率
- 注意处理非交易时间的数据间隔

### 3. 模型训练
- 使用交叉验证评估模型性能
- 注意数据泄露，确保时间序列的前向验证
- 监控数据分布变化，及时调整模型

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。